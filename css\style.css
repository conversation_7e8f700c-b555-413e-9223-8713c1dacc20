/* CSS Variables for Arabic Correspondence System */
:root {
    --primary-color: #2563eb;
    --secondary-color: #7c3aed;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --info-color: #0891b2;
    --light-bg: #f8fafc;
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --border-radius: 8px;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
    --arabic-font: 'Cairo', 'Tajawal', 'Segoe UI', Tahoma, Arial, sans-serif;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    direction: rtl;
}

body {
    font-family: var(--arabic-font);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--light-bg);
    direction: rtl;
    text-align: right;
}

/* Hide mobile menu toggle by default */
.nav-toggle {
    display: none;
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
.p-0 { padding: 0; }
.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.p-4 { padding: 2rem; }

/* Flexbox utilities */
.d-flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.gap-1 { gap: 0.5rem; }
.gap-2 { gap: 1rem; }
.gap-3 { gap: 1.5rem; }
.gap-4 { gap: 2rem; }

/* Grid utilities */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-auto { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }

/* Color utilities */
.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-info { color: var(--info-color); }
.text-gray-500 { color: var(--gray-500); }
.text-gray-600 { color: var(--gray-600); }
.text-gray-700 { color: var(--gray-700); }
.text-gray-800 { color: var(--gray-800); }

.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-danger { background-color: var(--danger-color); }
.bg-info { background-color: var(--info-color); }
.bg-white { background-color: var(--white); }
.bg-gray-50 { background-color: var(--gray-50); }
.bg-gray-100 { background-color: var(--gray-100); }
.bg-gray-200 { background-color: var(--gray-200); }

/* Border utilities */
.border { border: 1px solid var(--gray-200); }
.border-primary { border-color: var(--primary-color); }
.border-success { border-color: var(--success-color); }
.border-warning { border-color: var(--warning-color); }
.border-danger { border-color: var(--danger-color); }
.border-radius { border-radius: var(--border-radius); }
.border-radius-lg { border-radius: 12px; }
.border-radius-xl { border-radius: 16px; }

/* Shadow utilities */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-none { box-shadow: none; }

/* Width and height utilities */
.w-full { width: 100%; }
.w-auto { width: auto; }
.h-full { height: 100%; }
.h-auto { height: auto; }
.min-h-screen { min-height: 100vh; }

/* Position utilities */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* Overflow utilities */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }

/* Transition utilities */
.transition { transition: var(--transition); }
.transition-fast { transition: all 0.15s ease; }
.transition-slow { transition: all 0.5s ease; }

/* Cursor utilities */
.cursor-pointer { cursor: pointer; }
.cursor-default { cursor: default; }
.cursor-not-allowed { cursor: not-allowed; }

/* Font utilities */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }

/* Responsive utilities */
@media (max-width: 640px) {
    .sm\:hidden { display: none; }
    .sm\:block { display: block; }
    .sm\:flex { display: flex; }
    .sm\:grid { display: grid; }
    .sm\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
    .sm\:text-center { text-align: center; }
    .sm\:text-sm { font-size: 0.875rem; }
    .sm\:p-2 { padding: 1rem; }
    .sm\:m-2 { margin: 1rem; }
}

@media (max-width: 768px) {
    .md\:hidden { display: none; }
    .md\:block { display: block; }
    .md\:flex { display: flex; }
    .md\:grid { display: grid; }
    .md\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
    .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .md\:text-center { text-align: center; }
    .md\:text-base { font-size: 1rem; }
    .md\:p-3 { padding: 1.5rem; }
    .md\:m-3 { margin: 1.5rem; }
}

@media (max-width: 1024px) {
    .lg\:hidden { display: none; }
    .lg\:block { display: block; }
    .lg\:flex { display: flex; }
    .lg\:grid { display: grid; }
    .lg\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
    .lg\:text-lg { font-size: 1.125rem; }
    .lg\:p-4 { padding: 2rem; }
    .lg\:m-4 { margin: 2rem; }
}

/* Animation utilities */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes slideInUp {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}

@keyframes slideInDown {
    from { transform: translateY(-100%); }
    to { transform: translateY(0); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
    40%, 43% { transform: translate3d(0, -30px, 0); }
    70% { transform: translate3d(0, -15px, 0); }
    90% { transform: translate3d(0, -4px, 0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.animate-fadeIn { animation: fadeIn 0.3s ease-in-out; }
.animate-slideInRight { animation: slideInRight 0.3s ease-out; }
.animate-slideInLeft { animation: slideInLeft 0.3s ease-out; }
.animate-slideInUp { animation: slideInUp 0.3s ease-out; }
.animate-slideInDown { animation: slideInDown 0.3s ease-out; }
.animate-bounce { animation: bounce 1s infinite; }
.animate-pulse { animation: pulse 2s infinite; }
.animate-spin { animation: spin 1s linear infinite; }

/* Hover effects */
.hover\:bg-primary:hover { background-color: var(--primary-color); }
.hover\:bg-secondary:hover { background-color: var(--secondary-color); }
.hover\:bg-success:hover { background-color: var(--success-color); }
.hover\:bg-warning:hover { background-color: var(--warning-color); }
.hover\:bg-danger:hover { background-color: var(--danger-color); }
.hover\:bg-gray-100:hover { background-color: var(--gray-100); }
.hover\:bg-gray-200:hover { background-color: var(--gray-200); }

.hover\:text-primary:hover { color: var(--primary-color); }
.hover\:text-white:hover { color: var(--white); }
.hover\:text-gray-700:hover { color: var(--gray-700); }

.hover\:shadow-md:hover { box-shadow: var(--shadow-md); }
.hover\:shadow-lg:hover { box-shadow: var(--shadow-lg); }

.hover\:scale-105:hover { transform: scale(1.05); }
.hover\:scale-110:hover { transform: scale(1.1); }
.hover\:-translate-y-1:hover { transform: translateY(-4px); }
.hover\:-translate-y-2:hover { transform: translateY(-8px); }

/* Focus effects */
.focus\:outline-none:focus { outline: none; }
.focus\:ring-2:focus { box-shadow: 0 0 0 2px var(--primary-color); }
.focus\:ring-primary:focus { box-shadow: 0 0 0 2px var(--primary-color); }
.focus\:ring-success:focus { box-shadow: 0 0 0 2px var(--success-color); }
.focus\:ring-danger:focus { box-shadow: 0 0 0 2px var(--danger-color); }

/* Print styles */
@media print {
    .no-print { display: none !important; }
    .print-only { display: block !important; }
    
    body {
        background: white !important;
        color: black !important;
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .page {
        page-break-inside: avoid;
    }
    
    .correspondence-item {
        page-break-inside: avoid;
        border: 1px solid #ccc;
        margin-bottom: 10pt;
        padding: 10pt;
    }
    
    .navbar,
    .modal,
    .btn,
    .action-icon {
        display: none !important;
    }
    
    .main-content {
        margin-top: 0 !important;
    }
    
    .container {
        max-width: none !important;
        padding: 0 !important;
    }
}
