// التطبيق الرئيسي - نظام المراسلات
class CorrespondenceApp {
    constructor() {
        this.currentPage = 'dashboard';
        this.currentFiles = [];
        this.selectedItems = new Set();
        this.currentEditingId = null;
        this.currentEditingType = null;
        
        this.init();
    }

    // تهيئة التطبيق
    init() {
        this.initEventListeners();
        this.initNavigation();
        this.initFileUpload();
        this.loadDashboard();
        this.createBackupIfNeeded();
    }

    // تهيئة مستمعي الأحداث
    initEventListeners() {
        // التنقل
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.getAttribute('data-page');
                this.showPage(page);
            });
        });

        // القائمة المحمولة
        const mobileMenu = document.getElementById('mobile-menu');
        const navMenu = document.querySelector('.nav-menu');
        
        if (mobileMenu && navMenu) {
            mobileMenu.addEventListener('click', () => {
                mobileMenu.classList.toggle('active');
                navMenu.classList.toggle('active');
            });
        }

        // إغلاق النوافذ المنبثقة
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal();
            }
        });

        // مفاتيح الاختصار
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
                this.closeFilePreview();
            }
        });
    }

    // تهيئة التنقل
    initNavigation() {
        // تحديث الرابط النشط
        this.updateActiveNavLink('dashboard');
    }

    // تحديث الرابط النشط في شريط التنقل
    updateActiveNavLink(page) {
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('data-page') === page) {
                link.classList.add('active');
            }
        });
    }

    // عرض صفحة معينة
    showPage(page) {
        // إخفاء جميع الصفحات
        document.querySelectorAll('.page').forEach(p => {
            p.classList.remove('active');
        });

        // عرض الصفحة المطلوبة
        const targetPage = document.getElementById(`${page}-page`);
        if (targetPage) {
            targetPage.classList.add('active');
            this.currentPage = page;
            this.updateActiveNavLink(page);

            // تحميل محتوى الصفحة
            switch (page) {
                case 'dashboard':
                    this.loadDashboard();
                    break;
                case 'incoming':
                    this.loadIncomingCorrespondences();
                    break;
                case 'outgoing':
                    this.loadOutgoingCorrespondences();
                    break;
                case 'search':
                    this.loadSearchPage();
                    break;
                case 'settings':
                    this.loadSettings();
                    break;
            }

            // إغلاق القائمة المحمولة
            const mobileMenu = document.getElementById('mobile-menu');
            const navMenu = document.querySelector('.nav-menu');
            if (mobileMenu && navMenu) {
                mobileMenu.classList.remove('active');
                navMenu.classList.remove('active');
            }
        }
    }

    // تحميل لوحة التحكم
    loadDashboard() {
        const stats = storage.getStatistics();
        
        // تحديث الإحصائيات
        document.getElementById('incoming-count').textContent = stats.incoming;
        document.getElementById('outgoing-count').textContent = stats.outgoing;
        document.getElementById('pending-count').textContent = stats.pending;
        document.getElementById('total-count').textContent = stats.total;

        // تحميل آخر المراسلات
        this.loadRecentCorrespondences();
    }

    // تحميل آخر المراسلات
    loadRecentCorrespondences() {
        const recentList = document.getElementById('recent-list');
        const recentCorrespondences = storage.getRecentCorrespondences(5);

        if (recentCorrespondences.length === 0) {
            recentList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-inbox"></i>
                    <h3>لا توجد مراسلات</h3>
                    <p>ابدأ بإضافة مراسلة جديدة</p>
                </div>
            `;
            return;
        }

        recentList.innerHTML = recentCorrespondences.map(item => 
            this.createCorrespondenceItemHTML(item)
        ).join('');
    }

    // إنشاء HTML لعنصر مراسلة
    createCorrespondenceItemHTML(item, showCheckbox = false) {
        const typeIcon = item.type === 'incoming' ? 'fas fa-inbox' : 'fas fa-paper-plane';
        const typeText = item.type === 'incoming' ? 'واردة' : 'صادرة';
        const statusClass = `status-${item.status}`;
        const statusText = this.getStatusText(item.status);
        const formattedDate = this.formatDate(item.date);
        
        const filesCount = item.files ? item.files.length : 0;
        const filesHTML = filesCount > 0 ? `
            <div class="item-files">
                ${item.files.slice(0, 3).map(file => `
                    <span class="file-badge" onclick="previewFile('${file.id}', ${JSON.stringify(item.files).replace(/"/g, '&quot;')})">
                        ${file.name}
                    </span>
                `).join('')}
                ${filesCount > 3 ? `<span class="file-badge">+${filesCount - 3} ملف آخر</span>` : ''}
            </div>
        ` : '';

        return `
            <div class="correspondence-item" data-id="${item.id}" data-type="${item.type}">
                ${showCheckbox ? `
                    <input type="checkbox" class="item-checkbox" value="${item.id}" 
                           onchange="toggleItemSelection('${item.type}', '${item.id}', this.checked)">
                ` : ''}
                <div class="item-content">
                    <div class="item-header">
                        <div class="item-number">
                            <i class="${typeIcon}"></i>
                            ${item.number} - ${typeText}
                        </div>
                        <div class="item-date">${formattedDate}</div>
                    </div>
                    <div class="item-subject">${item.subject}</div>
                    <div class="item-sender">${item.senderReceiver}</div>
                    ${filesHTML}
                </div>
                <div class="item-actions">
                    <span class="item-status ${statusClass}">${statusText}</span>
                    <button class="action-icon" onclick="editCorrespondence('${item.type}', '${item.id}')" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-icon" onclick="viewCorrespondence('${item.type}', '${item.id}')" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-icon danger" onclick="deleteCorrespondence('${item.type}', '${item.id}')" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }

    // الحصول على نص الحالة
    getStatusText(status) {
        const statusMap = {
            'pending': 'في انتظار الرد',
            'replied': 'تم الرد',
            'sent': 'تم الإرسال',
            'delivered': 'تم التسليم',
            'archived': 'مؤرشف'
        };
        return statusMap[status] || status;
    }

    // تنسيق التاريخ
    formatDate(dateString) {
        const date = new Date(dateString);
        const settings = storage.getSettings();
        
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        
        switch (settings.dateFormat) {
            case 'mm/dd/yyyy':
                return `${month}/${day}/${year}`;
            case 'yyyy-mm-dd':
                return `${year}-${month}-${day}`;
            default:
                return `${day}/${month}/${year}`;
        }
    }

    // تحميل المراسلات الواردة
    loadIncomingCorrespondences() {
        const correspondences = storage.getCorrespondencesByType('incoming');
        this.renderCorrespondenceTable('incoming', correspondences);
    }

    // تحميل المراسلات الصادرة
    loadOutgoingCorrespondences() {
        const correspondences = storage.getCorrespondencesByType('outgoing');
        this.renderCorrespondenceTable('outgoing', correspondences);
    }

    // عرض جدول المراسلات
    renderCorrespondenceTable(type, correspondences) {
        const tableContainer = document.getElementById(`${type}-table`);
        
        if (correspondences.length === 0) {
            tableContainer.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-inbox"></i>
                    <h3>لا توجد مراسلات ${type === 'incoming' ? 'واردة' : 'صادرة'}</h3>
                    <p>ابدأ بإضافة مراسلة جديدة</p>
                    <button class="btn btn-primary mt-3" onclick="showAddCorrespondence('${type}')">
                        <i class="fas fa-plus"></i>
                        إضافة مراسلة ${type === 'incoming' ? 'واردة' : 'صادرة'}
                    </button>
                </div>
            `;
            return;
        }

        tableContainer.innerHTML = correspondences.map(item => 
            this.createCorrespondenceItemHTML(item, true)
        ).join('');
    }

    // تحميل صفحة البحث
    loadSearchPage() {
        // تعيين التاريخ الافتراضي (آخر شهر)
        const today = new Date();
        const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
        
        document.getElementById('search-date-from').value = lastMonth.toISOString().split('T')[0];
        document.getElementById('search-date-to').value = today.toISOString().split('T')[0];
        
        // مسح النتائج السابقة
        document.getElementById('search-results').innerHTML = '';
    }

    // تحميل الإعدادات
    loadSettings() {
        const settings = storage.getSettings();
        
        document.getElementById('organization-name').value = settings.organizationName || '';
        document.getElementById('items-per-page').value = settings.itemsPerPage || 25;
        document.getElementById('date-format').value = settings.dateFormat || 'dd/mm/yyyy';
    }

    // تهيئة رفع الملفات
    initFileUpload() {
        const fileInput = document.getElementById('correspondence-files');
        const uploadArea = document.querySelector('.file-upload-area');

        if (fileInput && uploadArea) {
            // السحب والإفلات
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                this.handleFileSelection(files);
            });

            // اختيار الملفات
            fileInput.addEventListener('change', (e) => {
                this.handleFileSelection(e.target.files);
            });
        }
    }

    // معالجة اختيار الملفات
    async handleFileSelection(files) {
        if (files.length === 0) return;

        const result = await fileHandler.handleFileUpload(files);
        
        // إضافة الملفات الناجحة
        this.currentFiles = this.currentFiles.concat(result.files);
        
        // عرض الأخطاء إن وجدت
        if (result.errors.length > 0) {
            const errorMessages = result.errors.map(error => 
                `${error.fileName}: ${error.errors.join(', ')}`
            ).join('\n');
            showNotification(`أخطاء في رفع الملفات:\n${errorMessages}`, 'error');
        }

        // تحديث عرض الملفات
        this.updateFileDisplay();
        
        if (result.files.length > 0) {
            showNotification(`تم رفع ${result.files.length} ملف بنجاح`, 'success');
        }
    }

    // تحديث عرض الملفات
    updateFileDisplay() {
        const container = document.getElementById('uploaded-files');
        if (!container) return;

        container.innerHTML = '';
        
        this.currentFiles.forEach(file => {
            const fileElement = fileHandler.createFileElement(file, true);
            container.appendChild(fileElement);
        });
    }

    // إنشاء نسخة احتياطية إذا لزم الأمر
    createBackupIfNeeded() {
        const lastBackup = localStorage.getItem('last_backup_date');
        const today = new Date().toDateString();
        
        if (lastBackup !== today) {
            storage.createBackup();
            localStorage.setItem('last_backup_date', today);
        }
    }

    // إغلاق النافذة المنبثقة
    closeModal() {
        const modal = document.getElementById('correspondence-modal');
        if (modal) {
            modal.classList.remove('active');
            modal.style.display = 'none';
            this.resetForm();
        }
    }

    // إغلاق معاينة الملف
    closeFilePreview() {
        const modal = document.getElementById('file-preview-modal');
        if (modal) {
            modal.classList.remove('active');
            modal.style.display = 'none';
        }
    }

    // إعادة تعيين النموذج
    resetForm() {
        const form = document.getElementById('correspondence-form');
        if (form) {
            form.reset();
        }
        
        this.currentFiles = [];
        this.currentEditingId = null;
        this.currentEditingType = null;
        this.updateFileDisplay();
    }
}

// إنشاء مثيل التطبيق عند تحميل الصفحة
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new CorrespondenceApp();
});

// الوظائف العامة المستخدمة في HTML
function showPage(page) {
    if (app) app.showPage(page);
}

function showNotification(message, type = 'info') {
    // إزالة الإشعارات الموجودة
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());

    // إنشاء إشعار جديد
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;

    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);

    // عرض الإشعار
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    // إخفاء الإشعار بعد 4 ثوان
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 4000);
}

// عرض نافذة إضافة مراسلة
function showAddCorrespondence(type) {
    const modal = document.getElementById('correspondence-modal');
    const title = document.getElementById('modal-title');
    const senderReceiverLabel = document.getElementById('sender-receiver-label');

    // تعيين العنوان والتسميات
    title.textContent = type === 'incoming' ? 'إضافة مراسلة واردة' : 'إضافة مراسلة صادرة';
    senderReceiverLabel.textContent = type === 'incoming' ? 'المرسل: *' : 'المستقبل: *';

    // تعيين النوع
    document.getElementById('correspondence-type').value = type;

    // تعيين التاريخ الحالي
    document.getElementById('correspondence-date').value = new Date().toISOString().split('T')[0];

    // مسح النموذج
    app.resetForm();

    // عرض النافذة
    modal.classList.add('active');
    modal.style.display = 'flex';
}

// تعديل مراسلة
function editCorrespondence(type, id) {
    const correspondence = storage.getCorrespondence(type, id);
    if (!correspondence) {
        showNotification('المراسلة غير موجودة', 'error');
        return;
    }

    const modal = document.getElementById('correspondence-modal');
    const title = document.getElementById('modal-title');
    const senderReceiverLabel = document.getElementById('sender-receiver-label');

    // تعيين العنوان والتسميات
    title.textContent = type === 'incoming' ? 'تعديل مراسلة واردة' : 'تعديل مراسلة صادرة';
    senderReceiverLabel.textContent = type === 'incoming' ? 'المرسل: *' : 'المستقبل: *';

    // ملء النموذج بالبيانات
    document.getElementById('correspondence-id').value = correspondence.id;
    document.getElementById('correspondence-type').value = type;
    document.getElementById('correspondence-number').value = correspondence.number;
    document.getElementById('correspondence-date').value = correspondence.date;
    document.getElementById('sender-receiver').value = correspondence.senderReceiver;
    document.getElementById('correspondence-status').value = correspondence.status;
    document.getElementById('correspondence-subject').value = correspondence.subject;
    document.getElementById('correspondence-content').value = correspondence.content || '';
    document.getElementById('correspondence-notes').value = correspondence.notes || '';

    // تحميل الملفات
    app.currentFiles = correspondence.files || [];
    app.currentEditingId = id;
    app.currentEditingType = type;
    app.updateFileDisplay();

    // عرض النافذة
    modal.classList.add('active');
    modal.style.display = 'flex';
}

// عرض مراسلة
function viewCorrespondence(type, id) {
    const correspondence = storage.getCorrespondence(type, id);
    if (!correspondence) {
        showNotification('المراسلة غير موجودة', 'error');
        return;
    }

    // إنشاء نافذة عرض
    const viewModal = document.createElement('div');
    viewModal.className = 'modal active';
    viewModal.style.display = 'flex';

    const typeText = type === 'incoming' ? 'واردة' : 'صادرة';
    const statusText = app.getStatusText(correspondence.status);
    const formattedDate = app.formatDate(correspondence.date);

    viewModal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2>عرض مراسلة ${typeText}</h2>
                <button class="close-btn" onclick="this.closest('.modal').remove()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="view-correspondence">
                    <div class="form-row">
                        <div class="form-group">
                            <label>رقم المراسلة:</label>
                            <p>${correspondence.number}</p>
                        </div>
                        <div class="form-group">
                            <label>التاريخ:</label>
                            <p>${formattedDate}</p>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>${type === 'incoming' ? 'المرسل' : 'المستقبل'}:</label>
                            <p>${correspondence.senderReceiver}</p>
                        </div>
                        <div class="form-group">
                            <label>الحالة:</label>
                            <p><span class="item-status status-${correspondence.status}">${statusText}</span></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>الموضوع:</label>
                        <p>${correspondence.subject}</p>
                    </div>
                    ${correspondence.content ? `
                        <div class="form-group">
                            <label>المحتوى:</label>
                            <p style="white-space: pre-wrap;">${correspondence.content}</p>
                        </div>
                    ` : ''}
                    ${correspondence.files && correspondence.files.length > 0 ? `
                        <div class="form-group">
                            <label>الملفات المرفقة:</label>
                            <div class="uploaded-files">
                                ${correspondence.files.map(file =>
                                    fileHandler.createFileElement(file, false).outerHTML
                                ).join('')}
                            </div>
                        </div>
                    ` : ''}
                    ${correspondence.notes ? `
                        <div class="form-group">
                            <label>ملاحظات:</label>
                            <p style="white-space: pre-wrap;">${correspondence.notes}</p>
                        </div>
                    ` : ''}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="editCorrespondence('${type}', '${id}'); this.closest('.modal').remove();">تعديل</button>
                <button type="button" class="btn btn-success" onclick="printCorrespondence('${type}', '${id}')">طباعة</button>
            </div>
        </div>
    `;

    document.body.appendChild(viewModal);
}

// حذف مراسلة
function deleteCorrespondence(type, id) {
    if (!confirm('هل أنت متأكد من حذف هذه المراسلة؟')) {
        return;
    }

    if (storage.deleteCorrespondence(type, id)) {
        showNotification('تم حذف المراسلة بنجاح', 'success');

        // تحديث العرض
        if (app.currentPage === 'dashboard') {
            app.loadDashboard();
        } else if (app.currentPage === type) {
            if (type === 'incoming') {
                app.loadIncomingCorrespondences();
            } else {
                app.loadOutgoingCorrespondences();
            }
        }
    } else {
        showNotification('خطأ في حذف المراسلة', 'error');
    }
}

// حفظ مراسلة
function saveCorrespondence() {
    const form = document.getElementById('correspondence-form');
    const formData = new FormData(form);

    // التحقق من صحة البيانات
    const number = document.getElementById('correspondence-number').value.trim();
    const date = document.getElementById('correspondence-date').value;
    const senderReceiver = document.getElementById('sender-receiver').value.trim();
    const subject = document.getElementById('correspondence-subject').value.trim();

    if (!number || !date || !senderReceiver || !subject) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    // إنشاء كائن المراسلة
    const correspondence = {
        number,
        date,
        senderReceiver,
        subject,
        status: document.getElementById('correspondence-status').value,
        content: document.getElementById('correspondence-content').value.trim(),
        notes: document.getElementById('correspondence-notes').value.trim(),
        files: app.currentFiles
    };

    const type = document.getElementById('correspondence-type').value;
    const editingId = app.currentEditingId;

    let success = false;

    if (editingId) {
        // تحديث مراسلة موجودة
        success = storage.updateCorrespondence(type, editingId, correspondence);
        if (success) {
            showNotification('تم تحديث المراسلة بنجاح', 'success');
        }
    } else {
        // إضافة مراسلة جديدة
        const newId = storage.addCorrespondence(type, correspondence);
        success = newId !== null;
        if (success) {
            showNotification('تم إضافة المراسلة بنجاح', 'success');
        }
    }

    if (success) {
        app.closeModal();

        // تحديث العرض
        if (app.currentPage === 'dashboard') {
            app.loadDashboard();
        } else if (app.currentPage === type) {
            if (type === 'incoming') {
                app.loadIncomingCorrespondences();
            } else {
                app.loadOutgoingCorrespondences();
            }
        }
    } else {
        showNotification('خطأ في حفظ المراسلة', 'error');
    }
}

// إغلاق النافذة المنبثقة
function closeModal() {
    if (app) app.closeModal();
}

// إغلاق معاينة الملف
function closeFilePreview() {
    if (app) app.closeFilePreview();
}

// معاينة ملف
function previewFile(fileId, filesJson) {
    let files;
    if (typeof filesJson === 'string') {
        try {
            files = JSON.parse(filesJson);
        } catch (e) {
            files = app.currentFiles;
        }
    } else {
        files = app.currentFiles;
    }

    fileHandler.previewFile(fileId, files);
}

// تحميل ملف
function downloadFile(fileId, filesJson) {
    let files;
    if (typeof filesJson === 'string') {
        try {
            files = JSON.parse(filesJson);
        } catch (e) {
            files = app.currentFiles;
        }
    } else {
        files = app.currentFiles;
    }

    fileHandler.downloadFile(fileId, files);
}

// حذف ملف
function removeFile(fileId) {
    app.currentFiles = app.currentFiles.filter(file => file.id !== fileId);
    app.updateFileDisplay();
    showNotification('تم حذف الملف', 'success');
}

// تبديل تحديد العنصر
function toggleItemSelection(type, id, checked) {
    const key = `${type}_${id}`;
    if (checked) {
        app.selectedItems.add(key);
    } else {
        app.selectedItems.delete(key);
    }
}

// تحديد جميع العناصر
function selectAllIncoming() {
    const checkboxes = document.querySelectorAll('#incoming-table .item-checkbox');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);

    checkboxes.forEach(cb => {
        cb.checked = !allChecked;
        toggleItemSelection('incoming', cb.value, cb.checked);
    });
}

function selectAllOutgoing() {
    const checkboxes = document.querySelectorAll('#outgoing-table .item-checkbox');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);

    checkboxes.forEach(cb => {
        cb.checked = !allChecked;
        toggleItemSelection('outgoing', cb.value, cb.checked);
    });
}

// حذف العناصر المحددة
function deleteSelectedIncoming() {
    const selectedIds = Array.from(app.selectedItems)
        .filter(key => key.startsWith('incoming_'))
        .map(key => key.replace('incoming_', ''));

    if (selectedIds.length === 0) {
        showNotification('لم يتم تحديد أي مراسلات', 'warning');
        return;
    }

    if (!confirm(`هل أنت متأكد من حذف ${selectedIds.length} مراسلة؟`)) {
        return;
    }

    if (storage.deleteMultipleCorrespondences('incoming', selectedIds)) {
        showNotification(`تم حذف ${selectedIds.length} مراسلة بنجاح`, 'success');
        app.selectedItems.clear();
        app.loadIncomingCorrespondences();
        app.loadDashboard();
    } else {
        showNotification('خطأ في حذف المراسلات', 'error');
    }
}

function deleteSelectedOutgoing() {
    const selectedIds = Array.from(app.selectedItems)
        .filter(key => key.startsWith('outgoing_'))
        .map(key => key.replace('outgoing_', ''));

    if (selectedIds.length === 0) {
        showNotification('لم يتم تحديد أي مراسلات', 'warning');
        return;
    }

    if (!confirm(`هل أنت متأكد من حذف ${selectedIds.length} مراسلة؟`)) {
        return;
    }

    if (storage.deleteMultipleCorrespondences('outgoing', selectedIds)) {
        showNotification(`تم حذف ${selectedIds.length} مراسلة بنجاح`, 'success');
        app.selectedItems.clear();
        app.loadOutgoingCorrespondences();
        app.loadDashboard();
    } else {
        showNotification('خطأ في حذف المراسلات', 'error');
    }
}

// فلترة المراسلات الواردة
function filterIncoming() {
    const filters = {
        search: document.getElementById('incoming-search').value.trim(),
        dateFrom: document.getElementById('incoming-date-from').value,
        dateTo: document.getElementById('incoming-date-to').value,
        status: document.getElementById('incoming-status').value
    };

    const correspondences = storage.getCorrespondencesByType('incoming', filters);
    app.renderCorrespondenceTable('incoming', correspondences);

    showNotification(`تم العثور على ${correspondences.length} مراسلة`, 'info');
}

// فلترة المراسلات الصادرة
function filterOutgoing() {
    const filters = {
        search: document.getElementById('outgoing-search').value.trim(),
        dateFrom: document.getElementById('outgoing-date-from').value,
        dateTo: document.getElementById('outgoing-date-to').value,
        status: document.getElementById('outgoing-status').value
    };

    const correspondences = storage.getCorrespondencesByType('outgoing', filters);
    app.renderCorrespondenceTable('outgoing', correspondences);

    showNotification(`تم العثور على ${correspondences.length} مراسلة`, 'info');
}

// البحث المتقدم
function performAdvancedSearch() {
    const filters = {
        type: document.getElementById('search-type').value,
        search: document.getElementById('search-text').value.trim(),
        dateFrom: document.getElementById('search-date-from').value,
        dateTo: document.getElementById('search-date-to').value,
        status: document.getElementById('search-status').value,
        hasFiles: document.getElementById('search-has-files').value
    };

    const results = storage.advancedSearch(filters);
    displaySearchResults(results);
}

// عرض نتائج البحث
function displaySearchResults(results) {
    const container = document.getElementById('search-results');

    if (results.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-search"></i>
                <h3>لا توجد نتائج</h3>
                <p>لم يتم العثور على مراسلات تطابق معايير البحث</p>
            </div>
        `;
        return;
    }

    container.innerHTML = `
        <div class="search-results-header">
            <h3>نتائج البحث (${results.length} مراسلة)</h3>
            <div class="search-actions">
                <button class="btn btn-success" onclick="generateReport()">
                    <i class="fas fa-file-pdf"></i>
                    إنشاء تقرير
                </button>
                <button class="btn btn-primary" onclick="exportSearchResults()">
                    <i class="fas fa-download"></i>
                    تصدير النتائج
                </button>
            </div>
        </div>
        <div class="search-results-list">
            ${results.map(item => app.createCorrespondenceItemHTML(item, false)).join('')}
        </div>
    `;

    showNotification(`تم العثور على ${results.length} مراسلة`, 'success');
}

// مسح نموذج البحث
function clearSearchForm() {
    document.getElementById('search-type').value = '';
    document.getElementById('search-text').value = '';
    document.getElementById('search-date-from').value = '';
    document.getElementById('search-date-to').value = '';
    document.getElementById('search-status').value = '';
    document.getElementById('search-has-files').value = '';
    document.getElementById('search-results').innerHTML = '';
}

// إنشاء تقرير
function generateReport() {
    const filters = {
        type: document.getElementById('search-type').value,
        search: document.getElementById('search-text').value.trim(),
        dateFrom: document.getElementById('search-date-from').value,
        dateTo: document.getElementById('search-date-to').value,
        status: document.getElementById('search-status').value,
        hasFiles: document.getElementById('search-has-files').value
    };

    const results = storage.advancedSearch(filters);

    if (results.length === 0) {
        showNotification('لا توجد بيانات لإنشاء التقرير', 'warning');
        return;
    }

    // إنشاء نافذة التقرير
    const reportModal = document.createElement('div');
    reportModal.className = 'modal active';
    reportModal.style.display = 'flex';

    const settings = storage.getSettings();
    const reportDate = new Date().toLocaleDateString('ar-SA');

    reportModal.innerHTML = `
        <div class="modal-content large">
            <div class="modal-header no-print">
                <h2>تقرير المراسلات</h2>
                <button class="close-btn" onclick="this.closest('.modal').remove()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="report-content">
                    <div class="report-header">
                        <h1>${settings.organizationName}</h1>
                        <h2>تقرير المراسلات</h2>
                        <p>تاريخ التقرير: ${reportDate}</p>
                        <p>عدد المراسلات: ${results.length}</p>
                    </div>

                    <div class="report-filters">
                        <h3>معايير البحث:</h3>
                        <ul>
                            ${filters.type ? `<li>النوع: ${filters.type === 'incoming' ? 'واردة' : 'صادرة'}</li>` : ''}
                            ${filters.search ? `<li>البحث: ${filters.search}</li>` : ''}
                            ${filters.dateFrom ? `<li>من تاريخ: ${app.formatDate(filters.dateFrom)}</li>` : ''}
                            ${filters.dateTo ? `<li>إلى تاريخ: ${app.formatDate(filters.dateTo)}</li>` : ''}
                            ${filters.status ? `<li>الحالة: ${app.getStatusText(filters.status)}</li>` : ''}
                        </ul>
                    </div>

                    <div class="report-data">
                        ${results.map(item => `
                            <div class="report-item">
                                <div class="item-header">
                                    <strong>${item.number}</strong> - ${item.type === 'incoming' ? 'واردة' : 'صادرة'}
                                    <span class="item-date">${app.formatDate(item.date)}</span>
                                </div>
                                <div class="item-details">
                                    <p><strong>الموضوع:</strong> ${item.subject}</p>
                                    <p><strong>${item.type === 'incoming' ? 'المرسل' : 'المستقبل'}:</strong> ${item.senderReceiver}</p>
                                    <p><strong>الحالة:</strong> ${app.getStatusText(item.status)}</p>
                                    ${item.content ? `<p><strong>المحتوى:</strong> ${item.content}</p>` : ''}
                                    ${item.files && item.files.length > 0 ? `<p><strong>الملفات:</strong> ${item.files.length} ملف</p>` : ''}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
            <div class="modal-footer no-print">
                <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print"></i>
                    طباعة
                </button>
                <button type="button" class="btn btn-success" onclick="exportReportAsPDF()">
                    <i class="fas fa-file-pdf"></i>
                    تصدير PDF
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(reportModal);
}

// طباعة مراسلة
function printCorrespondence(type, id) {
    const correspondence = storage.getCorrespondence(type, id);
    if (!correspondence) {
        showNotification('المراسلة غير موجودة', 'error');
        return;
    }

    const printWindow = window.open('', '_blank');
    const settings = storage.getSettings();
    const typeText = type === 'incoming' ? 'واردة' : 'صادرة';
    const statusText = app.getStatusText(correspondence.status);
    const formattedDate = app.formatDate(correspondence.date);

    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>طباعة مراسلة ${typeText}</title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; text-align: right; margin: 20px; }
                .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
                .field { margin-bottom: 15px; }
                .field label { font-weight: bold; display: inline-block; width: 120px; }
                .content { margin-top: 20px; padding: 15px; border: 1px solid #ddd; }
                .files { margin-top: 20px; }
                .file-item { padding: 5px; border-bottom: 1px solid #eee; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>${settings.organizationName}</h1>
                <h2>مراسلة ${typeText}</h2>
            </div>

            <div class="field">
                <label>رقم المراسلة:</label>
                <span>${correspondence.number}</span>
            </div>

            <div class="field">
                <label>التاريخ:</label>
                <span>${formattedDate}</span>
            </div>

            <div class="field">
                <label>${type === 'incoming' ? 'المرسل' : 'المستقبل'}:</label>
                <span>${correspondence.senderReceiver}</span>
            </div>

            <div class="field">
                <label>الحالة:</label>
                <span>${statusText}</span>
            </div>

            <div class="field">
                <label>الموضوع:</label>
                <span>${correspondence.subject}</span>
            </div>

            ${correspondence.content ? `
                <div class="content">
                    <label>المحتوى:</label>
                    <div style="margin-top: 10px; white-space: pre-wrap;">${correspondence.content}</div>
                </div>
            ` : ''}

            ${correspondence.files && correspondence.files.length > 0 ? `
                <div class="files">
                    <label>الملفات المرفقة:</label>
                    ${correspondence.files.map(file => `
                        <div class="file-item">${file.name} (${fileHandler.formatFileSize(file.size)})</div>
                    `).join('')}
                </div>
            ` : ''}

            ${correspondence.notes ? `
                <div class="content">
                    <label>ملاحظات:</label>
                    <div style="margin-top: 10px; white-space: pre-wrap;">${correspondence.notes}</div>
                </div>
            ` : ''}

            <script>
                window.onload = function() {
                    window.print();
                    window.onafterprint = function() {
                        window.close();
                    };
                };
            </script>
        </body>
        </html>
    `);

    printWindow.document.close();
}

// حفظ الإعدادات
function saveSettings() {
    const settings = {
        organizationName: document.getElementById('organization-name').value.trim(),
        itemsPerPage: parseInt(document.getElementById('items-per-page').value),
        dateFormat: document.getElementById('date-format').value
    };

    if (storage.saveSettings(settings)) {
        showNotification('تم حفظ الإعدادات بنجاح', 'success');
    } else {
        showNotification('خطأ في حفظ الإعدادات', 'error');
    }
}

// تصدير جميع البيانات
function exportAllData() {
    try {
        const data = storage.exportData();
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `correspondence_backup_${new Date().toISOString().split('T')[0]}.json`;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        URL.revokeObjectURL(url);
        showNotification('تم تصدير البيانات بنجاح', 'success');
    } catch (error) {
        console.error('خطأ في تصدير البيانات:', error);
        showNotification('خطأ في تصدير البيانات', 'error');
    }
}

// استيراد البيانات
function importData() {
    const fileInput = document.getElementById('import-file');
    const file = fileInput.files[0];

    if (!file) {
        showNotification('يرجى اختيار ملف للاستيراد', 'warning');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const jsonData = e.target.result;
            if (storage.importData(jsonData)) {
                showNotification('تم استيراد البيانات بنجاح', 'success');

                // إعادة تحميل التطبيق
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showNotification('خطأ في استيراد البيانات - تحقق من صحة الملف', 'error');
            }
        } catch (error) {
            console.error('خطأ في قراءة الملف:', error);
            showNotification('خطأ في قراءة الملف', 'error');
        }
    };

    reader.readAsText(file);
}

// مسح جميع البيانات
function clearAllData() {
    if (!confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
        return;
    }

    if (!confirm('تأكيد أخير: سيتم حذف جميع المراسلات والإعدادات نهائياً!')) {
        return;
    }

    if (storage.clearAllData()) {
        showNotification('تم مسح جميع البيانات', 'success');

        // إعادة تحميل التطبيق
        setTimeout(() => {
            location.reload();
        }, 1000);
    } else {
        showNotification('خطأ في مسح البيانات', 'error');
    }
}

// تصدير البيانات (من لوحة التحكم)
function exportData() {
    exportAllData();
}

// تصدير نتائج البحث
function exportSearchResults() {
    const filters = {
        type: document.getElementById('search-type').value,
        search: document.getElementById('search-text').value.trim(),
        dateFrom: document.getElementById('search-date-from').value,
        dateTo: document.getElementById('search-date-to').value,
        status: document.getElementById('search-status').value,
        hasFiles: document.getElementById('search-has-files').value
    };

    const results = storage.advancedSearch(filters);

    if (results.length === 0) {
        showNotification('لا توجد نتائج للتصدير', 'warning');
        return;
    }

    try {
        // تحويل النتائج إلى CSV
        const csvContent = convertToCSV(results);
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `search_results_${new Date().toISOString().split('T')[0]}.csv`;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        URL.revokeObjectURL(url);
        showNotification(`تم تصدير ${results.length} مراسلة`, 'success');
    } catch (error) {
        console.error('خطأ في تصدير النتائج:', error);
        showNotification('خطأ في تصدير النتائج', 'error');
    }
}

// تحويل البيانات إلى CSV
function convertToCSV(data) {
    const headers = ['الرقم', 'النوع', 'التاريخ', 'المرسل/المستقبل', 'الموضوع', 'الحالة', 'المحتوى', 'عدد الملفات'];
    const csvRows = [headers.join(',')];

    data.forEach(item => {
        const row = [
            `"${item.number}"`,
            `"${item.type === 'incoming' ? 'واردة' : 'صادرة'}"`,
            `"${app.formatDate(item.date)}"`,
            `"${item.senderReceiver}"`,
            `"${item.subject}"`,
            `"${app.getStatusText(item.status)}"`,
            `"${(item.content || '').replace(/"/g, '""')}"`,
            `"${item.files ? item.files.length : 0}"`
        ];
        csvRows.push(row.join(','));
    });

    return '\uFEFF' + csvRows.join('\n'); // إضافة BOM للدعم العربي
}

// تصدير التقرير كـ PDF (محاكاة)
function exportReportAsPDF() {
    // في التطبيق الحقيقي، يمكن استخدام مكتبة jsPDF
    showNotification('ميزة تصدير PDF ستكون متاحة قريباً', 'info');

    // كحل بديل، يمكن طباعة الصفحة
    setTimeout(() => {
        window.print();
    }, 500);
}

// إضافة بيانات تجريبية
function addSampleData() {
    if (!confirm('هل تريد إضافة بيانات تجريبية للنظام؟')) {
        return;
    }

    const sampleIncoming = [
        {
            number: 'و/001/2024',
            date: '2024-01-15',
            senderReceiver: 'وزارة التعليم',
            subject: 'تعميم بشأن الامتحانات النهائية',
            status: 'replied',
            content: 'يرجى الاطلاع على التعليمات الجديدة للامتحانات النهائية للفصل الدراسي الأول.',
            notes: 'تم الرد بتاريخ 2024-01-20',
            files: []
        },
        {
            number: 'و/002/2024',
            date: '2024-01-20',
            senderReceiver: 'إدارة الموارد البشرية',
            subject: 'طلب تحديث البيانات الشخصية',
            status: 'pending',
            content: 'يرجى تحديث البيانات الشخصية في النظام الإلكتروني.',
            notes: '',
            files: []
        }
    ];

    const sampleOutgoing = [
        {
            number: 'ص/001/2024',
            date: '2024-01-18',
            senderReceiver: 'جامعة الملك سعود',
            subject: 'طلب شراكة أكاديمية',
            status: 'sent',
            content: 'نتطلع للتعاون معكم في مجال البحث العلمي والتطوير.',
            notes: 'تم الإرسال عبر البريد الإلكتروني',
            files: []
        },
        {
            number: 'ص/002/2024',
            date: '2024-01-22',
            senderReceiver: 'شركة التقنية المتقدمة',
            subject: 'استفسار عن الخدمات التقنية',
            status: 'delivered',
            content: 'نود الاستفسار عن خدماتكم في مجال تطوير الأنظمة.',
            notes: '',
            files: []
        }
    ];

    // إضافة البيانات التجريبية
    sampleIncoming.forEach(item => {
        storage.addCorrespondence('incoming', item);
    });

    sampleOutgoing.forEach(item => {
        storage.addCorrespondence('outgoing', item);
    });

    showNotification('تم إضافة البيانات التجريبية بنجاح', 'success');

    // تحديث العرض
    if (app.currentPage === 'dashboard') {
        app.loadDashboard();
    } else if (app.currentPage === 'incoming') {
        app.loadIncomingCorrespondences();
    } else if (app.currentPage === 'outgoing') {
        app.loadOutgoingCorrespondences();
    }
}

// إضافة زر البيانات التجريبية إلى الإعدادات
document.addEventListener('DOMContentLoaded', function() {
    // إضافة زر البيانات التجريبية إذا لم تكن موجودة
    setTimeout(() => {
        const settingsSection = document.querySelector('.settings-section');
        if (settingsSection && storage.getStatistics().total === 0) {
            const sampleDataButton = document.createElement('div');
            sampleDataButton.className = 'setting-item';
            sampleDataButton.innerHTML = `
                <button class="btn btn-info" onclick="addSampleData()">
                    <i class="fas fa-database"></i>
                    إضافة بيانات تجريبية
                </button>
                <small>لتجربة النظام مع بيانات وهمية</small>
            `;
            settingsSection.appendChild(sampleDataButton);
        }
    }, 1000);
});
