/* تصميم خاص باللغة العربية - نظام المراسلات */

/* الخطوط العربية */
:root {
    --arabic-font: 'Cairo', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON> UI', Tahoma, Arial, sans-serif;
    --primary-color: #2563eb;
    --secondary-color: #7c3aed;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --info-color: #0891b2;
    --light-bg: #f8fafc;
    --white: #ffffff;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    --border-radius: 8px;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
}

/* إعادة تعيين الأساسيات */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--arabic-font);
    direction: rtl;
    text-align: right;
    background-color: var(--light-bg);
    color: var(--gray-800);
    line-height: 1.6;
}

/* شريط التنقل */
.navbar {
    background: var(--white);
    box-shadow: var(--shadow-md);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    border-bottom: 3px solid var(--primary-color);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.2rem;
}

.nav-logo i {
    font-size: 1.5rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 0;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 15px 20px;
    text-decoration: none;
    color: var(--gray-600);
    font-weight: 500;
    transition: var(--transition);
    border-radius: var(--border-radius);
    margin: 0 2px;
}

.nav-link:hover,
.nav-link.active {
    background: var(--primary-color);
    color: var(--white);
}

.nav-link i {
    font-size: 1rem;
}

/* المحتوى الرئيسي */
.main-content {
    margin-top: 70px;
    min-height: calc(100vh - 70px);
    padding: 20px 0;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* الصفحات */
.page {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.page.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* رأس الصفحة */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 2px solid var(--gray-200);
}

.page-header h1 {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--gray-800);
    font-size: 2rem;
    font-weight: 700;
}

.page-header h1 i {
    color: var(--primary-color);
}

.page-header p {
    color: var(--gray-600);
    margin-top: 5px;
}

/* الأزرار */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: var(--border-radius);
    font-family: var(--arabic-font);
    font-size: 0.95rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: #1d4ed8;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--gray-500);
    color: var(--white);
}

.btn-secondary:hover {
    background: var(--gray-600);
}

.btn-success {
    background: var(--success-color);
    color: var(--white);
}

.btn-success:hover {
    background: #047857;
}

.btn-danger {
    background: var(--danger-color);
    color: var(--white);
}

.btn-danger:hover {
    background: #b91c1c;
}

.btn-outline {
    background: transparent;
    color: var(--gray-600);
    border: 2px solid var(--gray-300);
}

.btn-outline:hover {
    background: var(--gray-100);
    border-color: var(--gray-400);
}

/* بطاقات الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: var(--white);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: var(--transition);
    border-right: 4px solid transparent;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
}

.stat-icon.incoming {
    background: var(--info-color);
}

.stat-icon.outgoing {
    background: var(--success-color);
}

.stat-icon.pending {
    background: var(--warning-color);
}

.stat-icon.total {
    background: var(--primary-color);
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 5px;
}

.stat-info p {
    color: var(--gray-600);
    font-size: 0.9rem;
}

/* الإجراءات السريعة */
.quick-actions {
    background: var(--white);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 40px;
}

.quick-actions h2 {
    color: var(--gray-800);
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.action-btn {
    background: var(--white);
    border: 2px solid var(--gray-200);
    padding: 20px;
    border-radius: var(--border-radius);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
    font-family: var(--arabic-font);
    font-size: 0.95rem;
    color: var(--gray-700);
}

.action-btn:hover {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.action-btn i {
    font-size: 1.5rem;
}

/* قسم الفلاتر */
.filters-section {
    background: var(--white);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 30px;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-weight: 500;
    color: var(--gray-700);
    font-size: 0.9rem;
}

.filter-group input,
.filter-group select {
    padding: 10px 12px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-family: var(--arabic-font);
    font-size: 0.95rem;
    transition: var(--transition);
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* جداول المراسلات */
.correspondence-table {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.table-header {
    padding: 20px;
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.table-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.table-content {
    max-height: 600px;
    overflow-y: auto;
}

/* عناصر المراسلة */
.correspondence-item {
    display: grid;
    grid-template-columns: 40px 1fr auto;
    gap: 15px;
    padding: 20px;
    border-bottom: 1px solid var(--gray-100);
    transition: var(--transition);
    align-items: center;
}

.correspondence-item:hover {
    background: var(--gray-50);
}

.correspondence-item:last-child {
    border-bottom: none;
}

.item-checkbox {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.item-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.item-number {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 0.95rem;
}

.item-date {
    color: var(--gray-500);
    font-size: 0.85rem;
}

.item-subject {
    font-weight: 600;
    color: var(--gray-800);
    font-size: 1rem;
    margin-bottom: 5px;
}

.item-sender {
    color: var(--gray-600);
    font-size: 0.9rem;
}

.item-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

.status-pending {
    background: #fef3c7;
    color: #92400e;
}

.status-replied {
    background: #d1fae5;
    color: #065f46;
}

.status-sent {
    background: #dbeafe;
    color: #1e40af;
}

.status-delivered {
    background: #e0e7ff;
    color: #3730a3;
}

.status-archived {
    background: #f3f4f6;
    color: #374151;
}

.item-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.action-icon {
    width: 35px;
    height: 35px;
    border: none;
    background: transparent;
    color: var(--gray-500);
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.action-icon:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.action-icon.danger:hover {
    background: #fee2e2;
    color: var(--danger-color);
}

/* الملفات المرفقة */
.item-files {
    display: flex;
    gap: 5px;
    margin-top: 8px;
    flex-wrap: wrap;
}

.file-badge {
    background: var(--gray-100);
    color: var(--gray-700);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: var(--transition);
}

.file-badge:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

.modal-content.large {
    max-width: 1200px;
}

@keyframes modalSlideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.modal-header h2 {
    color: var(--gray-800);
    font-size: 1.5rem;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray-500);
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: var(--transition);
}

.close-btn:hover {
    background: var(--gray-200);
    color: var(--gray-700);
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    display: flex;
    justify-content: flex-start;
    gap: 10px;
    padding: 20px 25px;
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

/* النماذج */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 500;
    color: var(--gray-700);
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 15px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-family: var(--arabic-font);
    font-size: 0.95rem;
    transition: var(--transition);
    resize: vertical;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* منطقة رفع الملفات */
.file-upload-area {
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius);
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background: var(--gray-50);
}

.file-upload-area input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-upload-text i {
    font-size: 2rem;
    color: var(--gray-400);
    margin-bottom: 10px;
}

.file-upload-text p {
    color: var(--gray-600);
    font-weight: 500;
    margin-bottom: 5px;
}

.file-upload-text small {
    color: var(--gray-500);
    font-size: 0.85rem;
}

/* الملفات المرفوعة */
.uploaded-files {
    margin-top: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.uploaded-file {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 15px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
}

.file-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.file-icon {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.file-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.file-name {
    font-weight: 500;
    color: var(--gray-800);
    font-size: 0.9rem;
}

.file-size {
    color: var(--gray-500);
    font-size: 0.8rem;
}

.file-actions {
    display: flex;
    gap: 5px;
}

.file-action {
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: var(--transition);
}

.file-action:hover {
    background: var(--gray-200);
    color: var(--primary-color);
}

.file-action.danger:hover {
    background: #fee2e2;
    color: var(--danger-color);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        right: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: var(--white);
        flex-direction: column;
        padding: 20px 0;
        transition: var(--transition);
        box-shadow: var(--shadow-lg);
    }

    .nav-menu.active {
        right: 0;
    }

    .nav-toggle {
        display: flex;
        flex-direction: column;
        cursor: pointer;
        gap: 4px;
    }

    .bar {
        width: 25px;
        height: 3px;
        background: var(--gray-700);
        transition: var(--transition);
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .actions-grid {
        grid-template-columns: 1fr;
    }

    .filters-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .correspondence-item {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .item-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .table-actions {
        flex-direction: column;
    }
}

/* تحسينات إضافية */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: var(--gray-500);
}

.loading i {
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--gray-500);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: var(--gray-400);
}

.empty-state h3 {
    margin-bottom: 10px;
    color: var(--gray-600);
}

/* إشعارات النجاح والخطأ */
.notification {
    position: fixed;
    top: 90px;
    left: 20px;
    padding: 15px 20px;
    border-radius: var(--border-radius);
    color: var(--white);
    font-weight: 500;
    z-index: 3000;
    transform: translateX(-100%);
    transition: var(--transition);
    max-width: 400px;
    box-shadow: var(--shadow-lg);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: var(--success-color);
}

.notification.error {
    background: var(--danger-color);
}

.notification.info {
    background: var(--info-color);
}

.notification.warning {
    background: var(--warning-color);
}
