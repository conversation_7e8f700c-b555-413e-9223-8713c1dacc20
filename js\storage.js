// إدارة قاعدة البيانات المحلية - نظام المراسلات
class CorrespondenceStorage {
    constructor() {
        this.storageKey = 'correspondence_system_data';
        this.settingsKey = 'correspondence_system_settings';
        this.initializeStorage();
    }

    // تهيئة قاعدة البيانات
    initializeStorage() {
        if (!localStorage.getItem(this.storageKey)) {
            const initialData = {
                incoming: [],
                outgoing: [],
                nextId: 1,
                lastUpdated: new Date().toISOString()
            };
            localStorage.setItem(this.storageKey, JSON.stringify(initialData));
        }

        if (!localStorage.getItem(this.settingsKey)) {
            const defaultSettings = {
                organizationName: 'مؤسستي',
                itemsPerPage: 25,
                dateFormat: 'dd/mm/yyyy',
                language: 'ar',
                theme: 'light'
            };
            localStorage.setItem(this.settingsKey, JSON.stringify(defaultSettings));
        }
    }

    // الحصول على جميع البيانات
    getAllData() {
        try {
            const data = localStorage.getItem(this.storageKey);
            return data ? JSON.parse(data) : this.getDefaultData();
        } catch (error) {
            console.error('خطأ في قراءة البيانات:', error);
            return this.getDefaultData();
        }
    }

    // الحصول على البيانات الافتراضية
    getDefaultData() {
        return {
            incoming: [],
            outgoing: [],
            nextId: 1,
            lastUpdated: new Date().toISOString()
        };
    }

    // حفظ جميع البيانات
    saveAllData(data) {
        try {
            data.lastUpdated = new Date().toISOString();
            localStorage.setItem(this.storageKey, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
            return false;
        }
    }

    // إضافة مراسلة جديدة
    addCorrespondence(type, correspondence) {
        const data = this.getAllData();
        
        // إنشاء معرف فريد
        correspondence.id = data.nextId++;
        correspondence.createdAt = new Date().toISOString();
        correspondence.updatedAt = new Date().toISOString();
        
        // إضافة المراسلة للنوع المناسب
        if (type === 'incoming') {
            data.incoming.unshift(correspondence);
        } else if (type === 'outgoing') {
            data.outgoing.unshift(correspondence);
        }
        
        return this.saveAllData(data) ? correspondence.id : null;
    }

    // تحديث مراسلة موجودة
    updateCorrespondence(type, id, updatedData) {
        const data = this.getAllData();
        const list = type === 'incoming' ? data.incoming : data.outgoing;
        
        const index = list.findIndex(item => item.id === parseInt(id));
        if (index !== -1) {
            list[index] = { ...list[index], ...updatedData, updatedAt: new Date().toISOString() };
            return this.saveAllData(data);
        }
        
        return false;
    }

    // حذف مراسلة
    deleteCorrespondence(type, id) {
        const data = this.getAllData();
        const list = type === 'incoming' ? data.incoming : data.outgoing;
        
        const index = list.findIndex(item => item.id === parseInt(id));
        if (index !== -1) {
            list.splice(index, 1);
            return this.saveAllData(data);
        }
        
        return false;
    }

    // حذف مراسلات متعددة
    deleteMultipleCorrespondences(type, ids) {
        const data = this.getAllData();
        const list = type === 'incoming' ? data.incoming : data.outgoing;
        
        const idsToDelete = ids.map(id => parseInt(id));
        const filteredList = list.filter(item => !idsToDelete.includes(item.id));
        
        if (type === 'incoming') {
            data.incoming = filteredList;
        } else {
            data.outgoing = filteredList;
        }
        
        return this.saveAllData(data);
    }

    // الحصول على مراسلة واحدة
    getCorrespondence(type, id) {
        const data = this.getAllData();
        const list = type === 'incoming' ? data.incoming : data.outgoing;
        
        return list.find(item => item.id === parseInt(id)) || null;
    }

    // الحصول على المراسلات حسب النوع
    getCorrespondencesByType(type, filters = {}) {
        const data = this.getAllData();
        let list = type === 'incoming' ? data.incoming : data.outgoing;
        
        // تطبيق الفلاتر
        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            list = list.filter(item => 
                item.subject.toLowerCase().includes(searchTerm) ||
                item.senderReceiver.toLowerCase().includes(searchTerm) ||
                item.content.toLowerCase().includes(searchTerm) ||
                item.number.toLowerCase().includes(searchTerm)
            );
        }
        
        if (filters.dateFrom) {
            list = list.filter(item => new Date(item.date) >= new Date(filters.dateFrom));
        }
        
        if (filters.dateTo) {
            list = list.filter(item => new Date(item.date) <= new Date(filters.dateTo));
        }
        
        if (filters.status) {
            list = list.filter(item => item.status === filters.status);
        }
        
        if (filters.hasFiles !== undefined) {
            if (filters.hasFiles === 'yes') {
                list = list.filter(item => item.files && item.files.length > 0);
            } else if (filters.hasFiles === 'no') {
                list = list.filter(item => !item.files || item.files.length === 0);
            }
        }
        
        return list;
    }

    // البحث المتقدم
    advancedSearch(filters) {
        const data = this.getAllData();
        let results = [];
        
        // تحديد الأنواع المراد البحث فيها
        const typesToSearch = filters.type ? [filters.type] : ['incoming', 'outgoing'];
        
        typesToSearch.forEach(type => {
            const list = this.getCorrespondencesByType(type, filters);
            results = results.concat(list.map(item => ({ ...item, type })));
        });
        
        // ترتيب النتائج حسب التاريخ (الأحدث أولاً)
        results.sort((a, b) => new Date(b.date) - new Date(a.date));
        
        return results;
    }

    // الحصول على الإحصائيات
    getStatistics() {
        const data = this.getAllData();
        
        const incomingCount = data.incoming.length;
        const outgoingCount = data.outgoing.length;
        const totalCount = incomingCount + outgoingCount;
        
        // حساب المراسلات في انتظار الرد
        const pendingCount = data.incoming.filter(item => item.status === 'pending').length;
        
        // إحصائيات هذا الشهر
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();
        
        const thisMonthIncoming = data.incoming.filter(item => {
            const itemDate = new Date(item.date);
            return itemDate.getMonth() === currentMonth && itemDate.getFullYear() === currentYear;
        }).length;
        
        const thisMonthOutgoing = data.outgoing.filter(item => {
            const itemDate = new Date(item.date);
            return itemDate.getMonth() === currentMonth && itemDate.getFullYear() === currentYear;
        }).length;
        
        return {
            total: totalCount,
            incoming: incomingCount,
            outgoing: outgoingCount,
            pending: pendingCount,
            thisMonth: {
                incoming: thisMonthIncoming,
                outgoing: thisMonthOutgoing,
                total: thisMonthIncoming + thisMonthOutgoing
            }
        };
    }

    // الحصول على آخر المراسلات
    getRecentCorrespondences(limit = 10) {
        const data = this.getAllData();
        
        // دمج المراسلات الواردة والصادرة
        const allCorrespondences = [
            ...data.incoming.map(item => ({ ...item, type: 'incoming' })),
            ...data.outgoing.map(item => ({ ...item, type: 'outgoing' }))
        ];
        
        // ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
        allCorrespondences.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        
        return allCorrespondences.slice(0, limit);
    }

    // إعدادات النظام
    getSettings() {
        try {
            const settings = localStorage.getItem(this.settingsKey);
            return settings ? JSON.parse(settings) : this.getDefaultSettings();
        } catch (error) {
            console.error('خطأ في قراءة الإعدادات:', error);
            return this.getDefaultSettings();
        }
    }

    getDefaultSettings() {
        return {
            organizationName: 'مؤسستي',
            itemsPerPage: 25,
            dateFormat: 'dd/mm/yyyy',
            language: 'ar',
            theme: 'light'
        };
    }

    saveSettings(settings) {
        try {
            localStorage.setItem(this.settingsKey, JSON.stringify(settings));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
            return false;
        }
    }

    // تصدير البيانات
    exportData() {
        const data = this.getAllData();
        const settings = this.getSettings();
        
        const exportData = {
            data,
            settings,
            exportDate: new Date().toISOString(),
            version: '1.0'
        };
        
        return JSON.stringify(exportData, null, 2);
    }

    // استيراد البيانات
    importData(jsonData) {
        try {
            const importedData = JSON.parse(jsonData);
            
            // التحقق من صحة البيانات
            if (!importedData.data || !importedData.data.incoming || !importedData.data.outgoing) {
                throw new Error('تنسيق البيانات غير صحيح');
            }
            
            // حفظ البيانات المستوردة
            this.saveAllData(importedData.data);
            
            if (importedData.settings) {
                this.saveSettings(importedData.settings);
            }
            
            return true;
        } catch (error) {
            console.error('خطأ في استيراد البيانات:', error);
            return false;
        }
    }

    // مسح جميع البيانات
    clearAllData() {
        try {
            localStorage.removeItem(this.storageKey);
            localStorage.removeItem(this.settingsKey);
            this.initializeStorage();
            return true;
        } catch (error) {
            console.error('خطأ في مسح البيانات:', error);
            return false;
        }
    }

    // نسخ احتياطي تلقائي
    createBackup() {
        const backupData = this.exportData();
        const backupKey = `backup_${new Date().toISOString().split('T')[0]}`;
        
        try {
            localStorage.setItem(backupKey, backupData);
            
            // الاحتفاظ بآخر 7 نسخ احتياطية فقط
            const allKeys = Object.keys(localStorage);
            const backupKeys = allKeys.filter(key => key.startsWith('backup_')).sort();
            
            if (backupKeys.length > 7) {
                const keysToDelete = backupKeys.slice(0, backupKeys.length - 7);
                keysToDelete.forEach(key => localStorage.removeItem(key));
            }
            
            return true;
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            return false;
        }
    }

    // الحصول على النسخ الاحتياطية
    getBackups() {
        const allKeys = Object.keys(localStorage);
        const backupKeys = allKeys.filter(key => key.startsWith('backup_')).sort().reverse();
        
        return backupKeys.map(key => ({
            key,
            date: key.replace('backup_', ''),
            size: localStorage.getItem(key).length
        }));
    }

    // استعادة من نسخة احتياطية
    restoreFromBackup(backupKey) {
        try {
            const backupData = localStorage.getItem(backupKey);
            if (!backupData) {
                throw new Error('النسخة الاحتياطية غير موجودة');
            }
            
            return this.importData(backupData);
        } catch (error) {
            console.error('خطأ في استعادة النسخة الاحتياطية:', error);
            return false;
        }
    }
}

// إنشاء مثيل واحد من كلاس التخزين
const storage = new CorrespondenceStorage();
