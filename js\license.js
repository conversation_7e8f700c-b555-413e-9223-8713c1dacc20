// نظام إدارة التراخيص والتفعيل - نظام المراسلات
class LicenseManager {
    constructor() {
        this.licenseKey = 'correspondence_license';
        this.usageKey = 'correspondence_usage';
        this.maxUsage = 5; // الحد الأقصى للاستخدام بدون ترخيص
        this.secretKey = 'MORASALTE_2024_SECRET_KEY'; // مفتاح سري للتشفير
        
        this.init();
    }

    // تهيئة نظام الترخيص
    init() {
        this.checkLicense();
        this.trackUsage();
    }

    // توليد معرف فريد للجهاز
    generateDeviceId() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Device fingerprint', 2, 2);
        
        const fingerprint = canvas.toDataURL();
        const deviceInfo = {
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
            screen: `${screen.width}x${screen.height}`,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            fingerprint: fingerprint.slice(-50) // آخر 50 حرف من البصمة
        };
        
        return this.hashString(JSON.stringify(deviceInfo));
    }

    // تشفير النص
    hashString(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // تحويل إلى 32bit integer
        }
        return Math.abs(hash).toString(36);
    }

    // توليد كود التفعيل للمستخدم
    generateUserCode() {
        const deviceId = this.generateDeviceId();
        const timestamp = Date.now();
        const randomPart = Math.random().toString(36).substr(2, 8);
        
        const userCode = `${deviceId}-${timestamp.toString(36)}-${randomPart}`;
        return userCode.toUpperCase();
    }

    // توليد كود التفعيل من المطور
    generateActivationCode(userCode, expiryDays = 365) {
        try {
            const parts = userCode.split('-');
            if (parts.length !== 3) {
                throw new Error('كود المستخدم غير صحيح');
            }

            const deviceId = parts[0];
            const timestamp = parseInt(parts[1], 36);
            const expiryDate = Date.now() + (expiryDays * 24 * 60 * 60 * 1000);
            
            // إنشاء كود التفعيل
            const activationData = {
                deviceId: deviceId,
                issued: timestamp,
                expiry: expiryDate,
                version: '1.0'
            };
            
            const activationString = JSON.stringify(activationData);
            const signature = this.createSignature(activationString);
            
            const activationCode = btoa(activationString) + '.' + signature;
            return activationCode;
            
        } catch (error) {
            console.error('خطأ في توليد كود التفعيل:', error);
            return null;
        }
    }

    // إنشاء توقيع للتحقق من صحة الكود
    createSignature(data) {
        const combined = data + this.secretKey;
        return this.hashString(combined);
    }

    // التحقق من صحة كود التفعيل
    validateActivationCode(activationCode) {
        try {
            const parts = activationCode.split('.');
            if (parts.length !== 2) {
                return { valid: false, error: 'تنسيق كود التفعيل غير صحيح' };
            }

            const activationString = atob(parts[0]);
            const signature = parts[1];
            
            // التحقق من التوقيع
            const expectedSignature = this.createSignature(activationString);
            if (signature !== expectedSignature) {
                return { valid: false, error: 'كود التفعيل غير صحيح أو تم تعديله' };
            }

            const activationData = JSON.parse(activationString);
            const currentDeviceId = this.generateDeviceId();
            
            // التحقق من معرف الجهاز
            if (activationData.deviceId !== currentDeviceId) {
                return { valid: false, error: 'كود التفعيل غير صالح لهذا الجهاز' };
            }

            // التحقق من انتهاء الصلاحية
            if (Date.now() > activationData.expiry) {
                return { valid: false, error: 'انتهت صلاحية كود التفعيل' };
            }

            return { 
                valid: true, 
                data: activationData,
                expiryDate: new Date(activationData.expiry)
            };
            
        } catch (error) {
            return { valid: false, error: 'خطأ في التحقق من كود التفعيل' };
        }
    }

    // حفظ الترخيص
    saveLicense(activationCode) {
        const validation = this.validateActivationCode(activationCode);
        if (validation.valid) {
            localStorage.setItem(this.licenseKey, activationCode);
            return true;
        }
        return false;
    }

    // التحقق من وجود ترخيص صالح
    isLicensed() {
        const license = localStorage.getItem(this.licenseKey);
        if (!license) return false;
        
        const validation = this.validateActivationCode(license);
        return validation.valid;
    }

    // الحصول على معلومات الترخيص
    getLicenseInfo() {
        const license = localStorage.getItem(this.licenseKey);
        if (!license) return null;
        
        const validation = this.validateActivationCode(license);
        if (validation.valid) {
            return {
                isActive: true,
                expiryDate: validation.expiryDate,
                daysRemaining: Math.ceil((validation.expiryDate - Date.now()) / (24 * 60 * 60 * 1000))
            };
        }
        return null;
    }

    // تتبع الاستخدام
    trackUsage() {
        const usage = this.getUsageCount();
        const newUsage = usage + 1;
        
        localStorage.setItem(this.usageKey, JSON.stringify({
            count: newUsage,
            lastUsed: Date.now(),
            firstUsed: usage === 0 ? Date.now() : this.getFirstUsedDate()
        }));
        
        return newUsage;
    }

    // الحصول على عدد مرات الاستخدام
    getUsageCount() {
        const usage = localStorage.getItem(this.usageKey);
        if (!usage) return 0;
        
        try {
            const data = JSON.parse(usage);
            return data.count || 0;
        } catch {
            return 0;
        }
    }

    // الحصول على تاريخ أول استخدام
    getFirstUsedDate() {
        const usage = localStorage.getItem(this.usageKey);
        if (!usage) return Date.now();
        
        try {
            const data = JSON.parse(usage);
            return data.firstUsed || Date.now();
        } catch {
            return Date.now();
        }
    }

    // التحقق من إمكانية الاستخدام
    canUse() {
        // إذا كان مرخص، يمكن الاستخدام
        if (this.isLicensed()) {
            return { allowed: true, reason: 'licensed' };
        }

        // التحقق من عدد مرات الاستخدام
        const usageCount = this.getUsageCount();
        if (usageCount < this.maxUsage) {
            return { 
                allowed: true, 
                reason: 'trial', 
                remaining: this.maxUsage - usageCount 
            };
        }

        return { 
            allowed: false, 
            reason: 'expired',
            message: 'انتهت فترة التجربة. يرجى الحصول على ترخيص للمتابعة.'
        };
    }

    // فحص الترخيص عند بدء التشغيل
    checkLicense() {
        const canUse = this.canUse();

        if (!canUse.allowed) {
            // تأخير عرض نافذة الترخيص للسماح بتحميل الصفحة
            setTimeout(() => {
                this.showLicenseDialog();
            }, 2000);
            return false;
        }

        if (canUse.reason === 'trial') {
            this.showTrialNotification(canUse.remaining);
        }

        return true;
    }

    // عرض نافذة الترخيص
    showLicenseDialog() {
        const userCode = this.generateUserCode();
        
        const modal = document.createElement('div');
        modal.className = 'license-modal';
        modal.innerHTML = `
            <div class="license-content">
                <div class="license-header">
                    <h2>🔐 تفعيل نظام المراسلات</h2>
                    <p>انتهت فترة التجربة المجانية</p>
                </div>
                
                <div class="license-body">
                    <div class="step">
                        <h3>الخطوة 1: نسخ كود الجهاز</h3>
                        <p>انسخ الكود التالي وأرسله للمطور:</p>
                        <div class="code-box">
                            <input type="text" id="user-code" value="${userCode}" readonly>
                            <button onclick="copyUserCode()" class="copy-btn">نسخ</button>
                        </div>
                    </div>
                    
                    <div class="step">
                        <h3>الخطوة 2: إدخال كود التفعيل</h3>
                        <p>أدخل كود التفعيل الذي حصلت عليه من المطور:</p>
                        <div class="activation-input">
                            <input type="text" id="activation-code" placeholder="أدخل كود التفعيل هنا">
                            <button onclick="activateSystem()" class="activate-btn">تفعيل</button>
                        </div>
                    </div>
                    
                    <div class="contact-info">
                        <h4>للحصول على كود التفعيل:</h4>
                        <p>📧 البريد الإلكتروني: <EMAIL></p>
                        <p>📱 الهاتف: +966 50 123 4567</p>
                        <p>💬 واتساب: +966 50 123 4567</p>
                    </div>
                </div>
                
                <div class="license-footer">
                    <button onclick="closeApp()" class="close-btn">إغلاق البرنامج</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        this.addLicenseStyles();
    }

    // عرض إشعار فترة التجربة
    showTrialNotification(remaining) {
        if (remaining <= 2) { // تحذير عند بقاء مرتين أو أقل
            const notification = document.createElement('div');
            notification.className = 'trial-notification';
            notification.innerHTML = `
                <div class="trial-content">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>تبقى ${remaining} مرة استخدام من فترة التجربة</span>
                    <button onclick="this.parentElement.parentElement.remove()">×</button>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // إزالة الإشعار بعد 10 ثوان
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 10000);
        }
    }

    // إضافة أنماط نافذة الترخيص
    addLicenseStyles() {
        if (document.getElementById('license-styles')) return;
        
        const styles = document.createElement('style');
        styles.id = 'license-styles';
        styles.textContent = `
            .license-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                font-family: 'Cairo', Arial, sans-serif;
                direction: rtl;
            }
            
            .license-content {
                background: white;
                border-radius: 12px;
                padding: 30px;
                max-width: 600px;
                width: 90%;
                max-height: 90vh;
                overflow-y: auto;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            }
            
            .license-header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #e2e8f0;
                padding-bottom: 20px;
            }
            
            .license-header h2 {
                color: #2563eb;
                margin-bottom: 10px;
                font-size: 1.8rem;
            }
            
            .step {
                margin-bottom: 25px;
                padding: 20px;
                background: #f8fafc;
                border-radius: 8px;
                border-right: 4px solid #2563eb;
            }
            
            .step h3 {
                color: #1e293b;
                margin-bottom: 10px;
                font-size: 1.2rem;
            }
            
            .code-box {
                display: flex;
                gap: 10px;
                margin-top: 10px;
            }
            
            .code-box input {
                flex: 1;
                padding: 12px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                font-family: 'Courier New', monospace;
                font-size: 0.9rem;
                background: #fff;
            }
            
            .copy-btn, .activate-btn {
                padding: 12px 20px;
                background: #2563eb;
                color: white;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-weight: 500;
                transition: background 0.3s;
            }
            
            .copy-btn:hover, .activate-btn:hover {
                background: #1d4ed8;
            }
            
            .activation-input {
                display: flex;
                gap: 10px;
                margin-top: 10px;
            }
            
            .activation-input input {
                flex: 1;
                padding: 12px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                font-size: 1rem;
            }
            
            .contact-info {
                background: #eff6ff;
                padding: 20px;
                border-radius: 8px;
                margin-top: 20px;
            }
            
            .contact-info h4 {
                color: #1e40af;
                margin-bottom: 15px;
            }
            
            .contact-info p {
                margin: 8px 0;
                color: #374151;
            }
            
            .license-footer {
                text-align: center;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #e2e8f0;
            }
            
            .close-btn {
                padding: 12px 30px;
                background: #dc2626;
                color: white;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-weight: 500;
            }
            
            .close-btn:hover {
                background: #b91c1c;
            }
            
            .trial-notification {
                position: fixed;
                top: 90px;
                right: 20px;
                background: #fbbf24;
                color: #92400e;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 9999;
                font-weight: 500;
                direction: rtl;
            }
            
            .trial-content {
                display: flex;
                align-items: center;
                gap: 10px;
            }
            
            .trial-content button {
                background: none;
                border: none;
                color: #92400e;
                font-size: 1.2rem;
                cursor: pointer;
                margin-right: 10px;
            }
        `;
        
        document.head.appendChild(styles);
    }

    // إزالة الترخيص (للاختبار)
    removeLicense() {
        localStorage.removeItem(this.licenseKey);
    }

    // إعادة تعيين الاستخدام (للاختبار)
    resetUsage() {
        localStorage.removeItem(this.usageKey);
    }
}

// الوظائف العامة للنافذة المنبثقة
function copyUserCode() {
    const userCodeInput = document.getElementById('user-code');
    userCodeInput.select();
    document.execCommand('copy');
    
    const copyBtn = document.querySelector('.copy-btn');
    const originalText = copyBtn.textContent;
    copyBtn.textContent = 'تم النسخ!';
    copyBtn.style.background = '#059669';
    
    setTimeout(() => {
        copyBtn.textContent = originalText;
        copyBtn.style.background = '#2563eb';
    }, 2000);
}

function activateSystem() {
    const activationCode = document.getElementById('activation-code').value.trim();
    
    if (!activationCode) {
        alert('يرجى إدخال كود التفعيل');
        return;
    }
    
    if (licenseManager.saveLicense(activationCode)) {
        alert('تم تفعيل النظام بنجاح!');
        location.reload();
    } else {
        alert('كود التفعيل غير صحيح. يرجى التحقق من الكود والمحاولة مرة أخرى.');
    }
}

function closeApp() {
    if (confirm('هل أنت متأكد من إغلاق البرنامج؟')) {
        window.close();
        // إذا لم يتم إغلاق النافذة، إخفاء المحتوى
        document.body.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100vh; font-family: Arial, sans-serif; direction: rtl;">
                <div style="text-align: center;">
                    <h2>تم إغلاق البرنامج</h2>
                    <p>يرجى الحصول على ترخيص للمتابعة</p>
                </div>
            </div>
        `;
    }
}

// إنشاء مثيل مدير التراخيص
const licenseManager = new LicenseManager();
