<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المراسلات - نسخة المطور</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/arabic.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .developer-header {
            background: linear-gradient(135deg, #1e293b, #334155);
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .developer-tools {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .tool-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .tool-card h3 {
            color: #1e293b;
            margin-bottom: 20px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .code-output {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            word-break: break-all;
            min-height: 60px;
            position: relative;
        }
        
        .copy-button {
            position: absolute;
            top: 10px;
            left: 10px;
            background: #2563eb;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
        }
        
        .copy-button:hover {
            background: #1d4ed8;
        }
        
        .btn-generate {
            background: #059669;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            width: 100%;
            margin-top: 10px;
        }
        
        .btn-generate:hover {
            background: #047857;
        }
        
        .btn-validate {
            background: #7c3aed;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            width: 100%;
            margin-top: 10px;
        }
        
        .btn-validate:hover {
            background: #6d28d9;
        }
        
        .validation-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-weight: 500;
        }
        
        .validation-result.valid {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .validation-result.invalid {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #2563eb;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        .license-history {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
        }
        
        .license-item {
            padding: 15px;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .license-item:last-child {
            border-bottom: none;
        }
        
        .license-info {
            flex: 1;
        }
        
        .license-code {
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            color: #6b7280;
            margin-top: 5px;
        }
        
        .license-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .license-status.active {
            background: #d1fae5;
            color: #065f46;
        }
        
        .license-status.expired {
            background: #fee2e2;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <div class="developer-header">
        <h1><i class="fas fa-code"></i> نظام المراسلات - نسخة المطور</h1>
        <p>أدوات إدارة التراخيص وتوليد أكواد التفعيل</p>
    </div>

    <div class="stats-grid">
        <div class="stat-item">
            <div class="stat-number" id="total-licenses">0</div>
            <div class="stat-label">إجمالي التراخيص</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="active-licenses">0</div>
            <div class="stat-label">التراخيص النشطة</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="expired-licenses">0</div>
            <div class="stat-label">التراخيص المنتهية</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="today-licenses">0</div>
            <div class="stat-label">تراخيص اليوم</div>
        </div>
    </div>

    <div class="developer-tools">
        <!-- أداة توليد كود التفعيل -->
        <div class="tool-card">
            <h3><i class="fas fa-key"></i> توليد كود التفعيل</h3>
            
            <div class="form-group">
                <label>كود المستخدم:</label>
                <textarea id="user-code-input" rows="3" placeholder="الصق كود المستخدم هنا..."></textarea>
            </div>
            
            <div class="form-group">
                <label>مدة الصلاحية (بالأيام):</label>
                <select id="expiry-days">
                    <option value="30">30 يوم</option>
                    <option value="90">90 يوم</option>
                    <option value="180">180 يوم</option>
                    <option value="365" selected>سنة واحدة</option>
                    <option value="730">سنتان</option>
                    <option value="1095">3 سنوات</option>
                    <option value="0">بدون انتهاء</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>ملاحظات:</label>
                <input type="text" id="license-notes" placeholder="ملاحظات اختيارية...">
            </div>
            
            <button class="btn-generate" onclick="generateActivationCode()">
                <i class="fas fa-magic"></i> توليد كود التفعيل
            </button>
            
            <div class="form-group" style="margin-top: 20px;">
                <label>كود التفعيل:</label>
                <div class="code-output" id="activation-output">
                    <button class="copy-button" onclick="copyActivationCode()" style="display: none;">نسخ</button>
                    سيظهر كود التفعيل هنا...
                </div>
            </div>
        </div>

        <!-- أداة التحقق من كود التفعيل -->
        <div class="tool-card">
            <h3><i class="fas fa-shield-alt"></i> التحقق من كود التفعيل</h3>
            
            <div class="form-group">
                <label>كود التفعيل:</label>
                <textarea id="validation-code-input" rows="3" placeholder="الصق كود التفعيل للتحقق منه..."></textarea>
            </div>
            
            <button class="btn-validate" onclick="validateActivationCode()">
                <i class="fas fa-check-circle"></i> التحقق من الكود
            </button>
            
            <div id="validation-result"></div>
        </div>

        <!-- أداة إدارة التراخيص -->
        <div class="tool-card">
            <h3><i class="fas fa-list"></i> سجل التراخيص</h3>
            
            <div class="form-group">
                <button class="btn-generate" onclick="exportLicenses()">
                    <i class="fas fa-download"></i> تصدير سجل التراخيص
                </button>
            </div>
            
            <div class="license-history" id="license-history">
                <!-- سيتم ملؤها بواسطة JavaScript -->
            </div>
        </div>

        <!-- أداة الإحصائيات -->
        <div class="tool-card">
            <h3><i class="fas fa-chart-bar"></i> إحصائيات مفصلة</h3>
            
            <div class="form-group">
                <label>إحصائيات هذا الشهر:</label>
                <div style="background: #f8fafc; padding: 15px; border-radius: 6px;">
                    <p>التراخيص الجديدة: <strong id="month-new">0</strong></p>
                    <p>التراخيص المنتهية: <strong id="month-expired">0</strong></p>
                    <p>معدل التفعيل: <strong id="activation-rate">0%</strong></p>
                </div>
            </div>
            
            <div class="form-group">
                <button class="btn-validate" onclick="generateReport()">
                    <i class="fas fa-file-pdf"></i> إنشاء تقرير شامل
                </button>
            </div>
        </div>

        <!-- أدوات الصيانة -->
        <div class="tool-card">
            <h3><i class="fas fa-tools"></i> أدوات الصيانة</h3>
            
            <div class="form-group">
                <button class="btn-generate" onclick="cleanExpiredLicenses()">
                    <i class="fas fa-broom"></i> تنظيف التراخيص المنتهية
                </button>
            </div>
            
            <div class="form-group">
                <button class="btn-validate" onclick="backupLicenseData()">
                    <i class="fas fa-save"></i> نسخ احتياطي للبيانات
                </button>
            </div>
            
            <div class="form-group">
                <button class="btn-generate" onclick="testLicenseSystem()">
                    <i class="fas fa-vial"></i> اختبار نظام التراخيص
                </button>
            </div>
            
            <div class="form-group" style="border-top: 2px solid #fee2e2; padding-top: 20px; margin-top: 20px;">
                <label style="color: #dc2626;">منطقة خطر:</label>
                <button class="btn-generate" onclick="resetAllLicenses()" style="background: #dc2626;">
                    <i class="fas fa-exclamation-triangle"></i> إعادة تعيين جميع التراخيص
                </button>
            </div>
        </div>

        <!-- معلومات النظام -->
        <div class="tool-card">
            <h3><i class="fas fa-info-circle"></i> معلومات النظام</h3>
            
            <div style="background: #f8fafc; padding: 15px; border-radius: 6px; font-size: 0.9rem;">
                <p><strong>إصدار النظام:</strong> 1.0.0</p>
                <p><strong>تاريخ الإصدار:</strong> <span id="release-date"></span></p>
                <p><strong>المطور:</strong> فريق نظام المراسلات</p>
                <p><strong>نوع الترخيص:</strong> تجاري</p>
                <p><strong>آخر تحديث:</strong> <span id="last-update"></span></p>
            </div>
            
            <div class="form-group" style="margin-top: 20px;">
                <button class="btn-validate" onclick="checkForUpdates()">
                    <i class="fas fa-sync-alt"></i> فحص التحديثات
                </button>
            </div>
        </div>
    </div>

    <!-- تحميل ملفات JavaScript -->
    <script src="js/license.js"></script>
    <script>
        // نسخة المطور - أدوات إضافية
        class DeveloperTools {
            constructor() {
                this.licenseHistory = this.loadLicenseHistory();
                this.init();
            }

            init() {
                this.updateStats();
                this.loadLicenseHistory();
                this.updateSystemInfo();
            }

            // تحديث الإحصائيات
            updateStats() {
                const stats = this.calculateStats();
                document.getElementById('total-licenses').textContent = stats.total;
                document.getElementById('active-licenses').textContent = stats.active;
                document.getElementById('expired-licenses').textContent = stats.expired;
                document.getElementById('today-licenses').textContent = stats.today;
                document.getElementById('month-new').textContent = stats.monthNew;
                document.getElementById('month-expired').textContent = stats.monthExpired;
                document.getElementById('activation-rate').textContent = stats.activationRate + '%';
            }

            // حساب الإحصائيات
            calculateStats() {
                const history = this.licenseHistory;
                const now = Date.now();
                const today = new Date().toDateString();
                const thisMonth = new Date().getMonth();
                const thisYear = new Date().getFullYear();

                let total = history.length;
                let active = 0;
                let expired = 0;
                let todayCount = 0;
                let monthNew = 0;
                let monthExpired = 0;

                history.forEach(license => {
                    const licenseDate = new Date(license.created);
                    const expiryDate = new Date(license.expiry);

                    if (expiryDate > now) {
                        active++;
                    } else {
                        expired++;
                    }

                    if (licenseDate.toDateString() === today) {
                        todayCount++;
                    }

                    if (licenseDate.getMonth() === thisMonth && licenseDate.getFullYear() === thisYear) {
                        monthNew++;
                    }

                    if (expiryDate.getMonth() === thisMonth && expiryDate.getFullYear() === thisYear && expiryDate < now) {
                        monthExpired++;
                    }
                });

                const activationRate = total > 0 ? Math.round((active / total) * 100) : 0;

                return {
                    total,
                    active,
                    expired,
                    today: todayCount,
                    monthNew,
                    monthExpired,
                    activationRate
                };
            }

            // تحميل سجل التراخيص
            loadLicenseHistory() {
                const history = localStorage.getItem('license_history');
                return history ? JSON.parse(history) : [];
            }

            // حفظ سجل التراخيص
            saveLicenseHistory() {
                localStorage.setItem('license_history', JSON.stringify(this.licenseHistory));
            }

            // إضافة ترخيص جديد للسجل
            addLicenseToHistory(userCode, activationCode, expiryDays, notes) {
                const license = {
                    id: Date.now(),
                    userCode,
                    activationCode,
                    created: Date.now(),
                    expiry: expiryDays === 0 ? Date.now() + (100 * 365 * 24 * 60 * 60 * 1000) : Date.now() + (expiryDays * 24 * 60 * 60 * 1000),
                    notes: notes || '',
                    status: 'active'
                };

                this.licenseHistory.unshift(license);
                this.saveLicenseHistory();
                this.updateStats();
                this.displayLicenseHistory();
            }

            // عرض سجل التراخيص
            displayLicenseHistory() {
                const container = document.getElementById('license-history');
                
                if (this.licenseHistory.length === 0) {
                    container.innerHTML = '<div style="text-align: center; padding: 20px; color: #6b7280;">لا توجد تراخيص مسجلة</div>';
                    return;
                }

                container.innerHTML = this.licenseHistory.map(license => {
                    const isExpired = Date.now() > license.expiry;
                    const expiryDate = new Date(license.expiry).toLocaleDateString('ar-SA');
                    const createdDate = new Date(license.created).toLocaleDateString('ar-SA');

                    return `
                        <div class="license-item">
                            <div class="license-info">
                                <div><strong>تاريخ الإنشاء:</strong> ${createdDate}</div>
                                <div><strong>تاريخ الانتهاء:</strong> ${expiryDate}</div>
                                <div class="license-code">${license.activationCode.substring(0, 50)}...</div>
                                ${license.notes ? `<div><strong>ملاحظات:</strong> ${license.notes}</div>` : ''}
                            </div>
                            <div class="license-status ${isExpired ? 'expired' : 'active'}">
                                ${isExpired ? 'منتهي' : 'نشط'}
                            </div>
                        </div>
                    `;
                }).join('');
            }

            // تحديث معلومات النظام
            updateSystemInfo() {
                document.getElementById('release-date').textContent = '2024-01-01';
                document.getElementById('last-update').textContent = new Date().toLocaleDateString('ar-SA');
            }
        }

        // إنشاء مثيل أدوات المطور
        const devTools = new DeveloperTools();

        // الوظائف العامة
        function generateActivationCode() {
            const userCode = document.getElementById('user-code-input').value.trim();
            const expiryDays = parseInt(document.getElementById('expiry-days').value);
            const notes = document.getElementById('license-notes').value.trim();

            if (!userCode) {
                alert('يرجى إدخال كود المستخدم');
                return;
            }

            const activationCode = licenseManager.generateActivationCode(userCode, expiryDays);
            
            if (activationCode) {
                document.getElementById('activation-output').innerHTML = `
                    <button class="copy-button" onclick="copyActivationCode()">نسخ</button>
                    ${activationCode}
                `;
                
                // إضافة للسجل
                devTools.addLicenseToHistory(userCode, activationCode, expiryDays, notes);
                
                alert('تم توليد كود التفعيل بنجاح!');
            } else {
                alert('خطأ في توليد كود التفعيل. تحقق من كود المستخدم.');
            }
        }

        function copyActivationCode() {
            const output = document.getElementById('activation-output');
            const text = output.textContent.replace('نسخ', '').trim();
            
            navigator.clipboard.writeText(text).then(() => {
                const button = output.querySelector('.copy-button');
                button.textContent = 'تم النسخ!';
                button.style.background = '#059669';
                
                setTimeout(() => {
                    button.textContent = 'نسخ';
                    button.style.background = '#2563eb';
                }, 2000);
            });
        }

        function validateActivationCode() {
            const code = document.getElementById('validation-code-input').value.trim();
            
            if (!code) {
                alert('يرجى إدخال كود التفعيل');
                return;
            }

            const validation = licenseManager.validateActivationCode(code);
            const resultDiv = document.getElementById('validation-result');
            
            if (validation.valid) {
                const expiryDate = validation.expiryDate.toLocaleDateString('ar-SA');
                const daysRemaining = Math.ceil((validation.expiryDate - Date.now()) / (24 * 60 * 60 * 1000));
                
                resultDiv.innerHTML = `
                    <div class="validation-result valid">
                        <i class="fas fa-check-circle"></i>
                        <strong>كود التفعيل صحيح</strong><br>
                        تاريخ الانتهاء: ${expiryDate}<br>
                        الأيام المتبقية: ${daysRemaining} يوم
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="validation-result invalid">
                        <i class="fas fa-times-circle"></i>
                        <strong>كود التفعيل غير صحيح</strong><br>
                        ${validation.error}
                    </div>
                `;
            }
        }

        function exportLicenses() {
            const data = {
                licenses: devTools.licenseHistory,
                exported: new Date().toISOString(),
                stats: devTools.calculateStats()
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `licenses_export_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            URL.revokeObjectURL(url);
        }

        function generateReport() {
            alert('ميزة التقارير الشاملة ستكون متاحة في الإصدار القادم');
        }

        function cleanExpiredLicenses() {
            const before = devTools.licenseHistory.length;
            devTools.licenseHistory = devTools.licenseHistory.filter(license => Date.now() <= license.expiry);
            devTools.saveLicenseHistory();
            devTools.updateStats();
            devTools.displayLicenseHistory();
            
            const cleaned = before - devTools.licenseHistory.length;
            alert(`تم تنظيف ${cleaned} ترخيص منتهي الصلاحية`);
        }

        function backupLicenseData() {
            exportLicenses();
            alert('تم إنشاء نسخة احتياطية من بيانات التراخيص');
        }

        function testLicenseSystem() {
            // اختبار سريع لنظام التراخيص
            const testUserCode = licenseManager.generateUserCode();
            const testActivationCode = licenseManager.generateActivationCode(testUserCode, 30);
            const validation = licenseManager.validateActivationCode(testActivationCode);
            
            if (validation.valid) {
                alert('✅ نظام التراخيص يعمل بشكل صحيح');
            } else {
                alert('❌ خطأ في نظام التراخيص: ' + validation.error);
            }
        }

        function resetAllLicenses() {
            if (confirm('⚠️ تحذير: سيتم حذف جميع التراخيص نهائياً. هل أنت متأكد؟')) {
                if (confirm('تأكيد أخير: هذا الإجراء لا يمكن التراجع عنه!')) {
                    localStorage.removeItem('license_history');
                    devTools.licenseHistory = [];
                    devTools.updateStats();
                    devTools.displayLicenseHistory();
                    alert('تم إعادة تعيين جميع التراخيص');
                }
            }
        }

        function checkForUpdates() {
            alert('أنت تستخدم أحدث إصدار من النظام');
        }

        // تحميل البيانات عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', () => {
            devTools.displayLicenseHistory();
        });
    </script>
</body>
</html>
