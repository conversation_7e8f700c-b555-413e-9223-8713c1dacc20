# نظام إدارة المراسلات - Correspondence Management System

نظام شامل لإدارة المراسلات الصادرة والواردة مع إمكانيات متقدمة لرفع الملفات والبحث والتقارير. مبني بتقنيات الويب الحديثة مع دعم كامل للغة العربية.

## ✨ الميزات الرئيسية

### 📨 إدارة المراسلات
- **المراسلات الواردة والصادرة** - تنظيم شامل لجميع أنواع المراسلات
- **تتبع الحالات** - متابعة حالة كل مراسلة (في انتظار الرد، تم الرد، مؤرشف، إلخ)
- **ترقيم تلقائي** - نظام ترقيم منظم للمراسلات
- **تواريخ دقيقة** - تسجيل تواريخ الإنشاء والتحديث

### 📎 إدارة الملفات
- **رفع متعدد الملفات** - دعم رفع ملفات متعددة بالسحب والإفلات
- **أنواع ملفات متنوعة** - PDF, Word, Excel, صور, نصوص
- **معاينة الملفات** - عرض مباشر للصور و PDF
- **تحميل آمن** - تحميل الملفات المرفقة بسهولة
- **إدارة الأحجام** - التحكم في أحجام الملفات المسموحة

### 🔍 البحث والفلترة
- **بحث متقدم** - بحث في جميع حقول المراسلة
- **فلترة بالتاريخ** - البحث ضمن فترات زمنية محددة
- **فلترة بالحالة** - تصفية حسب حالة المراسلة
- **فلترة بالملفات** - البحث في المراسلات التي تحتوي على ملفات
- **نتائج فورية** - عرض النتائج بشكل مباشر

### 📊 التقارير والإحصائيات
- **لوحة تحكم شاملة** - إحصائيات مفصلة ومرئية
- **تقارير قابلة للطباعة** - إنشاء تقارير مهنية
- **تصدير البيانات** - تصدير إلى CSV و JSON
- **إحصائيات زمنية** - تتبع الأداء عبر الفترات

### 🎨 واجهة مستخدم متقدمة
- **تصميم عربي أصيل** - دعم كامل للغة العربية واتجاه RTL
- **تصميم متجاوب** - يعمل على جميع الأجهزة والشاشات
- **ألوان عصرية** - نظام ألوان احترافي وجذاب
- **تفاعل سلس** - انتقالات وحركات ناعمة
- **إشعارات ذكية** - تنبيهات واضحة للمستخدم

## 🏗️ هيكل المشروع

```
morasalte/
├── index.html              # الصفحة الرئيسية
├── css/
│   ├── style.css          # الأنماط الأساسية
│   └── arabic.css         # الأنماط العربية
├── js/
│   ├── app.js             # التطبيق الرئيسي
│   ├── storage.js         # إدارة قاعدة البيانات
│   └── fileHandler.js     # معالجة الملفات
├── assets/                # الملفات الثابتة
└── README.md              # دليل المشروع
```

## 🛠️ التقنيات المستخدمة

- **HTML5** - هيكل دلالي حديث
- **CSS3** - تصميم متقدم مع Grid و Flexbox
- **JavaScript ES6+** - برمجة تفاعلية حديثة
- **LocalStorage** - قاعدة بيانات محلية
- **Font Awesome** - أيقونات احترافية
- **Google Fonts** - خطوط عربية جميلة (Cairo, Tajawal)

## 🚀 البدء السريع

### 1. تحميل المشروع
```bash
# استنساخ المشروع
git clone [repository-url]
cd morasalte
```

### 2. تشغيل الخادم المحلي
```bash
# باستخدام Python
python -m http.server 8000

# باستخدام Node.js
npx http-server

# باستخدام PHP
php -S localhost:8000

# باستخدام Live Server في VS Code
# تثبيت إضافة Live Server ثم النقر بالزر الأيمن على index.html
```

### 3. فتح التطبيق
افتح المتصفح وانتقل إلى `http://localhost:8000`

## 📋 دليل الاستخدام

### إضافة مراسلة جديدة
1. انقر على "إضافة مراسلة واردة" أو "إضافة مراسلة صادرة"
2. املأ البيانات المطلوبة (الرقم، التاريخ، المرسل/المستقبل، الموضوع)
3. أضف المحتوى والملاحظات حسب الحاجة
4. ارفع الملفات المرفقة بالسحب والإفلات أو النقر
5. انقر "حفظ" لإضافة المراسلة

### البحث في المراسلات
1. انتقل إلى صفحة "البحث والتقارير"
2. حدد معايير البحث (النوع، التاريخ، الحالة، إلخ)
3. انقر "بحث" لعرض النتائج
4. يمكنك تصدير النتائج أو إنشاء تقرير

### إدارة الملفات
- **رفع**: اسحب الملفات إلى منطقة الرفع أو انقر للاختيار
- **معاينة**: انقر على اسم الملف لمعاينته
- **تحميل**: انقر على أيقونة التحميل
- **حذف**: انقر على أيقونة الحذف

### تصدير البيانات
1. انتقل إلى "الإعدادات"
2. انقر "تصدير جميع البيانات"
3. سيتم تحميل ملف JSON يحتوي على جميع البيانات

## ⚙️ الإعدادات والتخصيص

### تغيير اسم المؤسسة
```javascript
// في صفحة الإعدادات
document.getElementById('organization-name').value = 'اسم مؤسستك';
```

### تخصيص الألوان
```css
/* في ملف css/arabic.css */
:root {
    --primary-color: #2563eb;    /* اللون الأساسي */
    --secondary-color: #7c3aed;  /* اللون الثانوي */
    --success-color: #059669;    /* لون النجاح */
    --warning-color: #d97706;    /* لون التحذير */
    --danger-color: #dc2626;     /* لون الخطر */
}
```

### إضافة حقول جديدة
```javascript
// في ملف js/storage.js - تعديل هيكل المراسلة
const correspondence = {
    number: '',
    date: '',
    senderReceiver: '',
    subject: '',
    status: '',
    content: '',
    notes: '',
    files: [],
    // إضافة حقول جديدة هنا
    priority: 'normal',
    category: 'general'
};
```

## 🔧 الميزات المتقدمة

### النسخ الاحتياطي التلقائي
- يتم إنشاء نسخة احتياطية يومياً تلقائياً
- الاحتفاظ بآخر 7 نسخ احتياطية
- إمكانية الاستعادة من أي نسخة احتياطية

### البحث الذكي
- البحث في جميع الحقول النصية
- دعم البحث الجزئي
- فلترة متعددة المعايير
- حفظ معايير البحث المفضلة

### إدارة الحالات
- تتبع دورة حياة المراسلة
- تحديث تلقائي للحالات
- إشعارات للمراسلات المعلقة

## 📱 التوافق مع الأجهزة

### أجهزة سطح المكتب
- **الدقة**: 1200px وأعلى
- **المتصفحات**: Chrome, Firefox, Safari, Edge
- **الميزات**: جميع الميزات متاحة

### الأجهزة اللوحية
- **الدقة**: 768px - 1199px
- **التفاعل**: دعم اللمس
- **التنقل**: قائمة قابلة للطي

### الهواتف الذكية
- **الدقة**: أقل من 768px
- **التصميم**: مُحسّن للشاشات الصغيرة
- **الأداء**: محسّن للشبكات البطيئة

## 🔒 الأمان والخصوصية

### تخزين البيانات
- **محلي فقط**: جميع البيانات تُحفظ في متصفح المستخدم
- **لا توجد خوادم خارجية**: لا يتم إرسال البيانات لأي مكان
- **تشفير**: الملفات محفوظة بتشفير Base64

### النسخ الاحتياطية
- **تحكم كامل**: المستخدم يتحكم في النسخ الاحتياطية
- **تصدير آمن**: إمكانية تصدير البيانات بصيغة JSON
- **استيراد محمي**: التحقق من صحة البيانات عند الاستيراد

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**المشكلة**: لا تظهر الملفات المرفوعة
```javascript
// الحل: تحقق من دعم المتصفح لـ FileReader
if (!window.FileReader) {
    alert('متصفحك لا يدعم رفع الملفات');
}
```

**المشكلة**: البيانات لا تُحفظ
```javascript
// الحل: تحقق من دعم localStorage
if (!window.localStorage) {
    alert('متصفحك لا يدعم التخزين المحلي');
}
```

**المشكلة**: الخطوط العربية لا تظهر
```css
/* الحل: تأكد من تحميل الخطوط */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
```

## 📈 خطط التطوير المستقبلية

### الإصدار 2.0
- [ ] دعم قاعدة بيانات خارجية
- [ ] نظام المستخدمين والصلاحيات
- [ ] إشعارات البريد الإلكتروني
- [ ] تطبيق الهاتف المحمول

### الإصدار 1.5
- [ ] تصدير PDF محسّن
- [ ] قوالب المراسلات
- [ ] تقارير إحصائية متقدمة
- [ ] نظام الأرشفة التلقائية

## 🤝 المساهمة في المشروع

نرحب بمساهماتكم لتطوير النظام:

1. **Fork** المشروع
2. إنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. تنفيذ التغييرات (`git commit -m 'Add amazing feature'`)
4. رفع التغييرات (`git push origin feature/amazing-feature`)
5. فتح Pull Request

### إرشادات المساهمة
- اتبع نمط الكود الموجود
- أضف تعليقات باللغة العربية
- اختبر التغييرات على أجهزة مختلفة
- حدّث الوثائق عند الحاجة

## 📞 الدعم والمساعدة

### طرق التواصل
- **GitHub Issues**: لتقارير الأخطاء والاقتراحات
- **البريد الإلكتروني**: للاستفسارات العامة
- **الوثائق**: دليل شامل في الملف

### الأسئلة الشائعة

**س: هل يمكن استخدام النظام بدون إنترنت؟**
ج: نعم، النظام يعمل بالكامل بدون إنترنت بعد التحميل الأولي.

**س: ما هو الحد الأقصى لحجم الملفات؟**
ج: الحد الافتراضي 10 ميجابايت، ويمكن تعديله في الإعدادات.

**س: هل البيانات آمنة؟**
ج: نعم، جميع البيانات محفوظة محلياً في متصفحك فقط.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

- **Font Awesome** - للأيقونات الرائعة
- **Google Fonts** - للخطوط العربية الجميلة
- **المجتمع العربي** - للدعم والاقتراحات

---

**تم البناء بـ ❤️ للمجتمع العربي**

*نظام إدارة المراسلات - حل شامل ومجاني لإدارة مراسلاتك بكفاءة واحترافية*
