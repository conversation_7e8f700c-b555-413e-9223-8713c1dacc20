<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="نظام إدارة المراسلات الصادرة والواردة">
    <title>نظام المراسلات - إدارة شاملة للمراسلات</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/arabic.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-envelope-open-text"></i>
                <h2>نظام المراسلات</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#dashboard" class="nav-link active" data-page="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        لوحة التحكم
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#incoming" class="nav-link" data-page="incoming">
                        <i class="fas fa-inbox"></i>
                        المراسلات الواردة
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#outgoing" class="nav-link" data-page="outgoing">
                        <i class="fas fa-paper-plane"></i>
                        المراسلات الصادرة
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#search" class="nav-link" data-page="search">
                        <i class="fas fa-search"></i>
                        البحث والتقارير
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#settings" class="nav-link" data-page="settings">
                        <i class="fas fa-cog"></i>
                        الإعدادات
                    </a>
                </li>
            </ul>
            <div class="nav-toggle" id="mobile-menu">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- لوحة التحكم -->
        <section id="dashboard-page" class="page active">
            <div class="container">
                <div class="page-header">
                    <h1><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h1>
                    <p>نظرة عامة على المراسلات والإحصائيات</p>
                </div>

                <!-- الإحصائيات -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon incoming">
                            <i class="fas fa-inbox"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="incoming-count">0</h3>
                            <p>المراسلات الواردة</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon outgoing">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="outgoing-count">0</h3>
                            <p>المراسلات الصادرة</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon pending">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="pending-count">0</h3>
                            <p>في انتظار الرد</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon total">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-count">0</h3>
                            <p>إجمالي المراسلات</p>
                        </div>
                    </div>
                </div>

                <!-- الإجراءات السريعة -->
                <div class="quick-actions">
                    <h2>الإجراءات السريعة</h2>
                    <div class="actions-grid">
                        <button class="action-btn" onclick="showAddCorrespondence('incoming')">
                            <i class="fas fa-plus"></i>
                            إضافة مراسلة واردة
                        </button>
                        <button class="action-btn" onclick="showAddCorrespondence('outgoing')">
                            <i class="fas fa-plus"></i>
                            إضافة مراسلة صادرة
                        </button>
                        <button class="action-btn" onclick="showPage('search')">
                            <i class="fas fa-search"></i>
                            البحث في المراسلات
                        </button>
                        <button class="action-btn" onclick="exportData()">
                            <i class="fas fa-download"></i>
                            تصدير البيانات
                        </button>
                        <button class="action-btn" onclick="showDeviceCode()">
                            <i class="fas fa-qrcode"></i>
                            كود الترخيص
                        </button>
                        <button class="action-btn" onclick="logoutFromSystem()" style="background: #e74c3c;">
                            <i class="fas fa-sign-out-alt"></i>
                            تسجيل الخروج
                        </button>
                    </div>
                </div>

                <!-- آخر المراسلات -->
                <div class="recent-correspondence">
                    <h2>آخر المراسلات</h2>
                    <div class="correspondence-list" id="recent-list">
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </div>
                </div>
            </div>
        </section>

        <!-- صفحة المراسلات الواردة -->
        <section id="incoming-page" class="page">
            <div class="container">
                <div class="page-header">
                    <h1><i class="fas fa-inbox"></i> المراسلات الواردة</h1>
                    <button class="btn btn-primary" onclick="showAddCorrespondence('incoming')">
                        <i class="fas fa-plus"></i>
                        إضافة مراسلة واردة
                    </button>
                </div>

                <!-- فلاتر البحث -->
                <div class="filters-section">
                    <div class="filters-grid">
                        <div class="filter-group">
                            <label>البحث في النص:</label>
                            <input type="text" id="incoming-search" placeholder="ابحث في الموضوع أو المرسل...">
                        </div>
                        <div class="filter-group">
                            <label>من تاريخ:</label>
                            <input type="date" id="incoming-date-from">
                        </div>
                        <div class="filter-group">
                            <label>إلى تاريخ:</label>
                            <input type="date" id="incoming-date-to">
                        </div>
                        <div class="filter-group">
                            <label>الحالة:</label>
                            <select id="incoming-status">
                                <option value="">جميع الحالات</option>
                                <option value="pending">في انتظار الرد</option>
                                <option value="replied">تم الرد</option>
                                <option value="archived">مؤرشف</option>
                            </select>
                        </div>
                    </div>
                    <button class="btn btn-secondary" onclick="filterIncoming()">
                        <i class="fas fa-filter"></i>
                        تطبيق الفلاتر
                    </button>
                </div>

                <!-- قائمة المراسلات -->
                <div class="correspondence-table">
                    <div class="table-header">
                        <div class="table-actions">
                            <button class="btn btn-outline" onclick="selectAllIncoming()">
                                <i class="fas fa-check-square"></i>
                                تحديد الكل
                            </button>
                            <button class="btn btn-danger" onclick="deleteSelectedIncoming()">
                                <i class="fas fa-trash"></i>
                                حذف المحدد
                            </button>
                        </div>
                    </div>
                    <div class="table-content" id="incoming-table">
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </div>
                </div>
            </div>
        </section>

        <!-- صفحة المراسلات الصادرة -->
        <section id="outgoing-page" class="page">
            <div class="container">
                <div class="page-header">
                    <h1><i class="fas fa-paper-plane"></i> المراسلات الصادرة</h1>
                    <button class="btn btn-primary" onclick="showAddCorrespondence('outgoing')">
                        <i class="fas fa-plus"></i>
                        إضافة مراسلة صادرة
                    </button>
                </div>

                <!-- فلاتر البحث -->
                <div class="filters-section">
                    <div class="filters-grid">
                        <div class="filter-group">
                            <label>البحث في النص:</label>
                            <input type="text" id="outgoing-search" placeholder="ابحث في الموضوع أو المستقبل...">
                        </div>
                        <div class="filter-group">
                            <label>من تاريخ:</label>
                            <input type="date" id="outgoing-date-from">
                        </div>
                        <div class="filter-group">
                            <label>إلى تاريخ:</label>
                            <input type="date" id="outgoing-date-to">
                        </div>
                        <div class="filter-group">
                            <label>الحالة:</label>
                            <select id="outgoing-status">
                                <option value="">جميع الحالات</option>
                                <option value="sent">تم الإرسال</option>
                                <option value="delivered">تم التسليم</option>
                                <option value="replied">تم الرد عليها</option>
                            </select>
                        </div>
                    </div>
                    <button class="btn btn-secondary" onclick="filterOutgoing()">
                        <i class="fas fa-filter"></i>
                        تطبيق الفلاتر
                    </button>
                </div>

                <!-- قائمة المراسلات -->
                <div class="correspondence-table">
                    <div class="table-header">
                        <div class="table-actions">
                            <button class="btn btn-outline" onclick="selectAllOutgoing()">
                                <i class="fas fa-check-square"></i>
                                تحديد الكل
                            </button>
                            <button class="btn btn-danger" onclick="deleteSelectedOutgoing()">
                                <i class="fas fa-trash"></i>
                                حذف المحدد
                            </button>
                        </div>
                    </div>
                    <div class="table-content" id="outgoing-table">
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </div>
                </div>
            </div>
        </section>

        <!-- صفحة البحث والتقارير -->
        <section id="search-page" class="page">
            <div class="container">
                <div class="page-header">
                    <h1><i class="fas fa-search"></i> البحث والتقارير</h1>
                    <p>بحث متقدم وإنشاء تقارير مفصلة</p>
                </div>

                <!-- البحث المتقدم -->
                <div class="advanced-search">
                    <h2>البحث المتقدم</h2>
                    <div class="search-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label>نوع المراسلة:</label>
                                <select id="search-type">
                                    <option value="">جميع الأنواع</option>
                                    <option value="incoming">واردة</option>
                                    <option value="outgoing">صادرة</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>البحث في:</label>
                                <input type="text" id="search-text" placeholder="الموضوع، المرسل، المحتوى...">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>من تاريخ:</label>
                                <input type="date" id="search-date-from">
                            </div>
                            <div class="form-group">
                                <label>إلى تاريخ:</label>
                                <input type="date" id="search-date-to">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>الحالة:</label>
                                <select id="search-status">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending">في انتظار الرد</option>
                                    <option value="replied">تم الرد</option>
                                    <option value="sent">تم الإرسال</option>
                                    <option value="delivered">تم التسليم</option>
                                    <option value="archived">مؤرشف</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>يحتوي على ملفات:</label>
                                <select id="search-has-files">
                                    <option value="">الكل</option>
                                    <option value="yes">نعم</option>
                                    <option value="no">لا</option>
                                </select>
                            </div>
                        </div>
                        <div class="search-actions">
                            <button class="btn btn-primary" onclick="performAdvancedSearch()">
                                <i class="fas fa-search"></i>
                                بحث
                            </button>
                            <button class="btn btn-secondary" onclick="clearSearchForm()">
                                <i class="fas fa-times"></i>
                                مسح
                            </button>
                            <button class="btn btn-success" onclick="generateReport()">
                                <i class="fas fa-file-pdf"></i>
                                إنشاء تقرير
                            </button>
                        </div>
                    </div>
                </div>

                <!-- نتائج البحث -->
                <div class="search-results" id="search-results">
                    <!-- سيتم ملؤها بواسطة JavaScript -->
                </div>
            </div>
        </section>

        <!-- صفحة الإعدادات -->
        <section id="settings-page" class="page">
            <div class="container">
                <div class="page-header">
                    <h1><i class="fas fa-cog"></i> الإعدادات</h1>
                    <p>إعدادات النظام والتخصيص</p>
                </div>

                <div class="settings-grid">
                    <!-- إعدادات عامة -->
                    <div class="settings-section">
                        <h3>الإعدادات العامة</h3>
                        <div class="setting-item">
                            <label>اسم المؤسسة:</label>
                            <input type="text" id="organization-name" placeholder="اسم المؤسسة">
                        </div>
                        <div class="setting-item">
                            <label>عدد المراسلات في الصفحة:</label>
                            <select id="items-per-page">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        <div class="setting-item">
                            <label>تنسيق التاريخ:</label>
                            <select id="date-format">
                                <option value="dd/mm/yyyy">يوم/شهر/سنة</option>
                                <option value="mm/dd/yyyy">شهر/يوم/سنة</option>
                                <option value="yyyy-mm-dd">سنة-شهر-يوم</option>
                            </select>
                        </div>
                    </div>

                    <!-- إدارة البيانات -->
                    <div class="settings-section">
                        <h3>إدارة البيانات</h3>
                        <div class="setting-item">
                            <button class="btn btn-success" onclick="exportAllData()">
                                <i class="fas fa-download"></i>
                                تصدير جميع البيانات
                            </button>
                        </div>
                        <div class="setting-item">
                            <label>استيراد البيانات:</label>
                            <input type="file" id="import-file" accept=".json">
                            <button class="btn btn-primary" onclick="importData()">
                                <i class="fas fa-upload"></i>
                                استيراد
                            </button>
                        </div>
                        <div class="setting-item">
                            <button class="btn btn-danger" onclick="clearAllData()">
                                <i class="fas fa-trash"></i>
                                مسح جميع البيانات
                            </button>
                        </div>
                        <div class="setting-item" style="border-top: 2px solid #fee2e2; padding-top: 20px; margin-top: 20px;">
                            <label style="color: #dc2626;">أدوات التطوير والاختبار:</label>
                            <button class="btn btn-warning" onclick="resetTrialPeriod()" style="background: #f59e0b; margin: 5px;">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين فترة التجربة
                            </button>
                            <button class="btn btn-info" onclick="showLicenseStatus()" style="margin: 5px;">
                                <i class="fas fa-info"></i>
                                حالة الترخيص التفصيلية
                            </button>
                            <button class="btn btn-success" onclick="generateTestLicense()" style="background: #059669; margin: 5px;">
                                <i class="fas fa-key"></i>
                                إنشاء ترخيص تجريبي
                            </button>
                            <button class="btn btn-purple" onclick="testDeviceBinding()" style="background: #7c3aed; margin: 5px;">
                                <i class="fas fa-shield-alt"></i>
                                اختبار ربط الجهاز
                            </button>
                            <small style="display: block; color: #6b7280; margin-top: 10px;">
                                هذه الأدوات للاختبار والتطوير فقط
                            </small>
                        </div>
                    </div>
                </div>

                <div class="settings-actions">
                    <button class="btn btn-primary" onclick="saveSettings()">
                        <i class="fas fa-save"></i>
                        حفظ الإعدادات
                    </button>
                </div>
            </div>
        </section>
    </main>

    <!-- نافذة إضافة/تعديل المراسلة -->
    <div id="correspondence-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">إضافة مراسلة</h2>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="correspondence-form">
                    <input type="hidden" id="correspondence-id">
                    <input type="hidden" id="correspondence-type">

                    <div class="form-row">
                        <div class="form-group">
                            <label>رقم المراسلة: *</label>
                            <input type="text" id="correspondence-number" required>
                        </div>
                        <div class="form-group">
                            <label>التاريخ: *</label>
                            <input type="date" id="correspondence-date" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label id="sender-receiver-label">المرسل: *</label>
                            <input type="text" id="sender-receiver" required>
                        </div>
                        <div class="form-group">
                            <label>الحالة:</label>
                            <select id="correspondence-status">
                                <option value="pending">في انتظار الرد</option>
                                <option value="replied">تم الرد</option>
                                <option value="sent">تم الإرسال</option>
                                <option value="delivered">تم التسليم</option>
                                <option value="archived">مؤرشف</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>الموضوع: *</label>
                        <input type="text" id="correspondence-subject" required>
                    </div>

                    <div class="form-group">
                        <label>المحتوى:</label>
                        <textarea id="correspondence-content" rows="4"></textarea>
                    </div>

                    <div class="form-group">
                        <label>الملفات المرفقة:</label>
                        <div class="file-upload-area">
                            <input type="file" id="correspondence-files" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt">
                            <div class="file-upload-text">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <p>اسحب الملفات هنا أو انقر للاختيار</p>
                                <small>PDF, Word, صور, نصوص</small>
                            </div>
                        </div>
                        <div id="uploaded-files" class="uploaded-files">
                            <!-- سيتم عرض الملفات المرفوعة هنا -->
                        </div>
                    </div>

                    <div class="form-group">
                        <label>ملاحظات:</label>
                        <textarea id="correspondence-notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveCorrespondence()">حفظ</button>
            </div>
        </div>
    </div>

    <!-- نافذة معاينة الملف -->
    <div id="file-preview-modal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h2 id="file-preview-title">معاينة الملف</h2>
                <button class="close-btn" onclick="closeFilePreview()">&times;</button>
            </div>
            <div class="modal-body">
                <div id="file-preview-content">
                    <!-- سيتم عرض محتوى الملف هنا -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeFilePreview()">إغلاق</button>
                <button type="button" class="btn btn-primary" id="download-file-btn">تحميل</button>
            </div>
        </div>
    </div>

    <!-- تحميل ملفات JavaScript -->
    <script src="js/license.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/fileHandler.js"></script>
    <script src="js/app.js"></script>

    <script>
        // التحقق من الترخيص عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            // السماح بتحميل التطبيق أولاً
            setTimeout(() => {
                // التحقق من إمكانية الاستخدام
                const canUse = licenseManager.canUse();

                if (!canUse.allowed) {
                    // عرض نافذة الترخيص بدلاً من منع التحميل
                    licenseManager.showLicenseDialog();
                }

                // إضافة معلومات الترخيص لشريط التنقل
                addLicenseInfo();
            }, 1000);
        });

        // إضافة معلومات الترخيص
        function addLicenseInfo() {
            const licenseInfo = licenseManager.getLicenseInfo();
            const navContainer = document.querySelector('.nav-container');

            if (licenseInfo && licenseInfo.isActive) {
                // إضافة معلومات الترخيص للمستخدمين المرخصين
                const licenseIndicator = document.createElement('div');
                licenseIndicator.className = 'license-indicator';
                licenseIndicator.innerHTML = `
                    <i class="fas fa-shield-alt" style="color: #059669;"></i>
                    <span style="color: #059669; font-size: 0.8rem;">مرخص</span>
                `;
                licenseIndicator.style.cssText = `
                    display: flex;
                    align-items: center;
                    gap: 5px;
                    background: rgba(5, 150, 105, 0.1);
                    padding: 5px 10px;
                    border-radius: 15px;
                    border: 1px solid rgba(5, 150, 105, 0.3);
                `;
                navContainer.appendChild(licenseIndicator);

                // تحذير إذا كان الترخيص سينتهي قريباً
                if (licenseInfo.daysRemaining <= 30) {
                    setTimeout(() => {
                        showNotification(`تنبيه: سينتهي الترخيص خلال ${licenseInfo.daysRemaining} يوم`, 'warning');
                    }, 2000);
                }
            } else {
                // إضافة مؤشر فترة التجربة
                const canUse = licenseManager.canUse();
                if (canUse.allowed && canUse.reason === 'trial') {
                    const trialIndicator = document.createElement('div');
                    trialIndicator.className = 'trial-indicator';
                    trialIndicator.innerHTML = `
                        <i class="fas fa-clock" style="color: #d97706;"></i>
                        <span style="color: #d97706; font-size: 0.8rem;">تجربة (${canUse.remaining})</span>
                    `;
                    trialIndicator.style.cssText = `
                        display: flex;
                        align-items: center;
                        gap: 5px;
                        background: rgba(217, 119, 6, 0.1);
                        padding: 5px 10px;
                        border-radius: 15px;
                        border: 1px solid rgba(217, 119, 6, 0.3);
                        cursor: pointer;
                    `;

                    trialIndicator.onclick = function() {
                        if (confirm('هل تريد الحصول على ترخيص كامل الآن؟')) {
                            licenseManager.showLicenseDialog();
                        }
                    };

                    navContainer.appendChild(trialIndicator);
                }
            }
        }

        // إضافة قائمة ترخيص في الإعدادات
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const settingsSection = document.querySelector('#settings-page .settings-grid');
                if (settingsSection) {
                    const licenseSection = document.createElement('div');
                    licenseSection.className = 'settings-section';
                    licenseSection.innerHTML = `
                        <h3>معلومات الترخيص</h3>
                        <div id="license-info-display">
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </div>
                        <div class="setting-item">
                            <button class="btn btn-primary" onclick="showLicenseManagement()">
                                <i class="fas fa-key"></i>
                                إدارة الترخيص
                            </button>
                        </div>
                        <div class="setting-item">
                            <button class="btn btn-info" onclick="showDeviceCode()">
                                <i class="fas fa-qrcode"></i>
                                عرض كود الجهاز
                            </button>
                            <small>للحصول على ترخيص جديد أو تجديد الترخيص الحالي</small>
                        </div>
                    `;
                    settingsSection.appendChild(licenseSection);

                    updateLicenseDisplay();
                }
            }, 1000);
        });

        // تحديث عرض معلومات الترخيص
        function updateLicenseDisplay() {
            const display = document.getElementById('license-info-display');
            if (!display) return;

            const licenseInfo = licenseManager.getLicenseInfo();
            const canUse = licenseManager.canUse();

            if (licenseInfo && licenseInfo.isActive) {
                display.innerHTML = `
                    <div style="background: #d1fae5; padding: 15px; border-radius: 8px; border: 1px solid #a7f3d0;">
                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                            <i class="fas fa-shield-alt" style="color: #059669; font-size: 1.2rem;"></i>
                            <strong style="color: #065f46;">الترخيص نشط</strong>
                        </div>
                        <p style="color: #047857; margin: 5px 0;">تاريخ الانتهاء: ${licenseInfo.expiryDate.toLocaleDateString('ar-SA')}</p>
                        <p style="color: #047857; margin: 5px 0;">الأيام المتبقية: ${licenseInfo.daysRemaining} يوم</p>
                    </div>
                `;
            } else if (canUse.allowed && canUse.reason === 'trial') {
                display.innerHTML = `
                    <div style="background: #fef3c7; padding: 15px; border-radius: 8px; border: 1px solid #fcd34d;">
                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                            <i class="fas fa-clock" style="color: #d97706; font-size: 1.2rem;"></i>
                            <strong style="color: #92400e;">فترة تجربة</strong>
                        </div>
                        <p style="color: #b45309; margin: 5px 0;">الاستخدامات المتبقية: ${canUse.remaining} من ${licenseManager.maxUsage}</p>
                        <p style="color: #b45309; margin: 5px 0;">احصل على ترخيص كامل للاستمرار</p>
                    </div>
                `;
            } else {
                display.innerHTML = `
                    <div style="background: #fee2e2; padding: 15px; border-radius: 8px; border: 1px solid #fca5a5;">
                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                            <i class="fas fa-exclamation-triangle" style="color: #dc2626; font-size: 1.2rem;"></i>
                            <strong style="color: #991b1b;">غير مرخص</strong>
                        </div>
                        <p style="color: #b91c1c; margin: 5px 0;">انتهت فترة التجربة</p>
                        <p style="color: #b91c1c; margin: 5px 0;">يرجى الحصول على ترخيص للمتابعة</p>
                    </div>
                `;
            }
        }

        // عرض إدارة الترخيص
        function showLicenseManagement() {
            const licenseInfo = licenseManager.getLicenseInfo();
            const canUse = licenseManager.canUse();

            if (licenseInfo && licenseInfo.isActive) {
                // إظهار معلومات الترخيص النشط
                const modal = document.createElement('div');
                modal.className = 'modal active';
                modal.style.display = 'flex';
                modal.innerHTML = `
                    <div class="modal-content">
                        <div class="modal-header">
                            <h2><i class="fas fa-shield-alt"></i> معلومات الترخيص</h2>
                            <button class="close-btn" onclick="this.closest('.modal').remove()">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div style="background: #d1fae5; padding: 20px; border-radius: 8px; text-align: center;">
                                <i class="fas fa-check-circle" style="color: #059669; font-size: 3rem; margin-bottom: 15px;"></i>
                                <h3 style="color: #065f46; margin-bottom: 15px;">الترخيص نشط ومفعل</h3>
                                <p style="color: #047857;">تاريخ الانتهاء: ${licenseInfo.expiryDate.toLocaleDateString('ar-SA')}</p>
                                <p style="color: #047857;">الأيام المتبقية: ${licenseInfo.daysRemaining} يوم</p>
                            </div>

                            <div style="margin-top: 20px;">
                                <h4>خيارات الترخيص:</h4>
                                <button class="btn btn-secondary" onclick="renewLicense()" style="margin: 10px 5px;">
                                    <i class="fas fa-sync-alt"></i>
                                    تجديد الترخيص
                                </button>
                                <button class="btn btn-danger" onclick="removeLicenseConfirm()" style="margin: 10px 5px;">
                                    <i class="fas fa-trash"></i>
                                    إزالة الترخيص
                                </button>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">إغلاق</button>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
            } else {
                // إظهار نافذة التفعيل
                licenseManager.showLicenseDialog();
            }
        }

        // تجديد الترخيص
        function renewLicense() {
            alert('للتجديد، يرجى التواصل مع المطور للحصول على ترخيص جديد');
        }

        // تأكيد إزالة الترخيص
        function removeLicenseConfirm() {
            if (confirm('هل أنت متأكد من إزالة الترخيص؟ ستحتاج لترخيص جديد للمتابعة.')) {
                licenseManager.removeLicense();
                alert('تم إزالة الترخيص. ستحتاج لإعادة تشغيل البرنامج.');
                location.reload();
            }
        }

        // عرض كود الجهاز
        function showDeviceCode() {
            const deviceCode = licenseManager.generateUserCode();

            const modal = document.createElement('div');
            modal.className = 'modal active';
            modal.style.display = 'flex';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2><i class="fas fa-qrcode"></i> كود الجهاز للترخيص</h2>
                        <button class="close-btn" onclick="this.closest('.modal').remove()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div style="text-align: center; margin-bottom: 20px;">
                            <i class="fas fa-desktop" style="font-size: 3rem; color: #2563eb; margin-bottom: 15px;"></i>
                            <h3 style="color: #1e293b;">كود جهازك الفريد</h3>
                            <p style="color: #64748b;">أرسل هذا الكود للمطور للحصول على ترخيص</p>
                        </div>

                        <div style="background: #f8fafc; border: 2px solid #e2e8f0; border-radius: 8px; padding: 20px; margin: 20px 0;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <label style="font-weight: 600; color: #374151;">كود الجهاز:</label>
                                <button onclick="copyDeviceCode('${deviceCode}')" style="background: #2563eb; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 0.9rem;">
                                    <i class="fas fa-copy"></i> نسخ
                                </button>
                            </div>
                            <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #d1d5db; font-family: 'Courier New', monospace; font-size: 0.9rem; word-break: break-all; line-height: 1.6;">
                                ${deviceCode}
                            </div>
                        </div>

                        <div style="background: #eff6ff; border: 1px solid #bfdbfe; border-radius: 8px; padding: 20px; margin: 20px 0;">
                            <h4 style="color: #1e40af; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                                <i class="fas fa-info-circle"></i>
                                خطوات الحصول على الترخيص:
                            </h4>
                            <ol style="color: #1e40af; line-height: 1.8; padding-right: 20px;">
                                <li><strong>انسخ الكود أعلاه</strong> باستخدام زر "نسخ"</li>
                                <li><strong>أرسل الكود للمطور</strong> عبر إحدى الطرق التالية:</li>
                                <ul style="margin: 10px 0; padding-right: 20px;">
                                    <li>📧 البريد الإلكتروني: <strong><EMAIL></strong></li>
                                    <li>📱 الهاتف: <strong>+966 50 123 4567</strong></li>
                                    <li>💬 واتساب: <strong>+966 50 123 4567</strong></li>
                                </ul>
                                <li><strong>احصل على كود التفعيل</strong> من المطور</li>
                                <li><strong>أدخل كود التفعيل</strong> في نافذة التفعيل</li>
                            </ol>
                        </div>

                        <div style="background: #fef3c7; border: 1px solid #fcd34d; border-radius: 8px; padding: 15px; margin: 20px 0;">
                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                <i class="fas fa-exclamation-triangle" style="color: #d97706;"></i>
                                <strong style="color: #92400e;">ملاحظة مهمة:</strong>
                            </div>
                            <p style="color: #b45309; font-size: 0.9rem; line-height: 1.6;">
                                هذا الكود مرتبط بجهازك فقط ولا يمكن استخدامه على أجهزة أخرى.
                                احتفظ بكود التفعيل في مكان آمن لاستخدامه مستقبلاً.
                            </p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">إغلاق</button>
                        <button class="btn btn-primary" onclick="copyDeviceCode('${deviceCode}')">
                            <i class="fas fa-copy"></i>
                            نسخ الكود
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // نسخ كود الجهاز
        function copyDeviceCode(code) {
            navigator.clipboard.writeText(code).then(() => {
                // تحديث جميع أزرار النسخ في النافذة
                const copyButtons = document.querySelectorAll('button[onclick*="copyDeviceCode"]');
                copyButtons.forEach(btn => {
                    const originalText = btn.innerHTML;
                    btn.innerHTML = '<i class="fas fa-check"></i> تم النسخ!';
                    btn.style.background = '#059669';

                    setTimeout(() => {
                        btn.innerHTML = originalText;
                        btn.style.background = '#2563eb';
                    }, 2000);
                });

                showNotification('تم نسخ كود الجهاز بنجاح!', 'success');
            }).catch(() => {
                // في حالة فشل النسخ التلقائي
                const textArea = document.createElement('textarea');
                textArea.value = code;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                showNotification('تم نسخ كود الجهاز!', 'success');
            });
        }

        // إعادة تعيين فترة التجربة (للاختبار)
        function resetTrialPeriod() {
            if (confirm('هل تريد إعادة تعيين فترة التجربة؟ (للاختبار فقط)')) {
                licenseManager.resetUsage();
                licenseManager.removeLicense();
                showNotification('تم إعادة تعيين فترة التجربة. أعد تحميل الصفحة.', 'success');
                setTimeout(() => {
                    location.reload();
                }, 2000);
            }
        }

        // عرض حالة الترخيص التفصيلية
        function showLicenseStatus() {
            const licenseInfo = licenseManager.getLicenseInfo();
            const canUse = licenseManager.canUse();
            const usageCount = licenseManager.getUsageCount();
            const deviceId = licenseManager.generateDeviceId();

            const modal = document.createElement('div');
            modal.className = 'modal active';
            modal.style.display = 'flex';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2><i class="fas fa-info-circle"></i> حالة الترخيص التفصيلية</h2>
                        <button class="close-btn" onclick="this.closest('.modal').remove()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h4 style="margin-bottom: 15px; color: #1e293b;">معلومات الجهاز:</h4>
                            <p><strong>معرف الجهاز:</strong> <code style="background: #e2e8f0; padding: 2px 6px; border-radius: 4px;">${deviceId}</code></p>
                            <p><strong>عدد مرات الاستخدام:</strong> ${usageCount} من ${licenseManager.maxUsage}</p>
                            <p><strong>يمكن الاستخدام:</strong> ${canUse.allowed ? '✅ نعم' : '❌ لا'}</p>
                            <p><strong>السبب:</strong> ${canUse.reason === 'licensed' ? 'مرخص' : canUse.reason === 'trial' ? 'فترة تجربة' : 'انتهت الفترة'}</p>
                        </div>

                        ${licenseInfo ? `
                            <div style="background: #d1fae5; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                                <h4 style="margin-bottom: 15px; color: #065f46;">معلومات الترخيص:</h4>
                                <p><strong>الحالة:</strong> ✅ نشط</p>
                                <p><strong>تاريخ الانتهاء:</strong> ${licenseInfo.expiryDate.toLocaleDateString('ar-SA')}</p>
                                <p><strong>الأيام المتبقية:</strong> ${licenseInfo.daysRemaining} يوم</p>
                            </div>
                        ` : `
                            <div style="background: #fee2e2; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                                <h4 style="margin-bottom: 15px; color: #991b1b;">حالة الترخيص:</h4>
                                <p><strong>الحالة:</strong> ❌ غير مرخص</p>
                                <p><strong>الاستخدامات المتبقية:</strong> ${canUse.allowed ? canUse.remaining || 0 : 0}</p>
                            </div>
                        `}

                        <div style="background: #eff6ff; padding: 20px; border-radius: 8px;">
                            <h4 style="margin-bottom: 15px; color: #1e40af;">معلومات تقنية:</h4>
                            <p><strong>المتصفح:</strong> ${navigator.userAgent.split(' ')[0]}</p>
                            <p><strong>اللغة:</strong> ${navigator.language}</p>
                            <p><strong>المنطقة الزمنية:</strong> ${Intl.DateTimeFormat().resolvedOptions().timeZone}</p>
                            <p><strong>دقة الشاشة:</strong> ${screen.width}x${screen.height}</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">إغلاق</button>
                        <button class="btn btn-primary" onclick="copyDeviceCode('${deviceId}')">
                            <i class="fas fa-copy"></i>
                            نسخ معرف الجهاز
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // إنشاء ترخيص تجريبي للاختبار
        function generateTestLicense() {
            if (confirm('هل تريد إنشاء ترخيص تجريبي لهذا الجهاز؟')) {
                const deviceId = licenseManager.generateDeviceId();
                const userCode = licenseManager.generateUserCode();

                // إنشاء كود تفعيل تجريبي
                const activationCode = licenseManager.generateActivationCode(userCode, 365);

                if (activationCode) {
                    // تطبيق الترخيص مباشرة
                    const success = licenseManager.activateLicense(activationCode);

                    if (success) {
                        showNotification('تم إنشاء وتطبيق ترخيص تجريبي بنجاح! صالح لمدة سنة.', 'success');
                        setTimeout(() => {
                            location.reload();
                        }, 2000);
                    } else {
                        showNotification('فشل في تطبيق الترخيص التجريبي', 'error');
                    }
                } else {
                    showNotification('فشل في إنشاء الترخيص التجريبي', 'error');
                }
            }
        }

        // اختبار ربط الجهاز
        function testDeviceBinding() {
            const currentDeviceId = licenseManager.generateDeviceId();
            const currentUserCode = licenseManager.generateUserCode();

            // محاكاة جهاز مختلف
            const fakeDeviceId = 'FAKE-DEVICE-ID-12345';
            const fakeUserCode = 'FAKE-USER-CODE-67890';

            const modal = document.createElement('div');
            modal.className = 'modal active';
            modal.style.display = 'flex';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2><i class="fas fa-shield-alt"></i> اختبار ربط الجهاز</h2>
                        <button class="close-btn" onclick="this.closest('.modal').remove()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h4 style="margin-bottom: 15px; color: #1e293b;">🔍 معلومات الجهاز الحالي:</h4>
                            <p><strong>معرف الجهاز:</strong></p>
                            <code style="background: #e2e8f0; padding: 8px; border-radius: 4px; display: block; word-break: break-all; margin: 10px 0;">${currentDeviceId}</code>
                            <p><strong>كود المستخدم:</strong></p>
                            <code style="background: #e2e8f0; padding: 8px; border-radius: 4px; display: block; word-break: break-all; margin: 10px 0;">${currentUserCode}</code>
                        </div>

                        <div style="background: #fef3c7; border: 1px solid #fcd34d; border-radius: 8px; padding: 20px; margin: 20px 0;">
                            <h4 style="color: #92400e; margin-bottom: 15px;">🧪 اختبار الحماية:</h4>
                            <p style="color: #b45309; line-height: 1.6;">
                                لاختبار قوة نظام الحماية، جرب الخطوات التالية:
                            </p>
                            <ol style="color: #b45309; line-height: 1.8; padding-right: 20px; margin: 15px 0;">
                                <li><strong>انسخ كود التفعيل</strong> الذي حصلت عليه</li>
                                <li><strong>افتح النظام في متصفح آخر</strong> (Chrome, Firefox, Edge)</li>
                                <li><strong>أو افتح النظام في نافذة خفية/سرية</strong></li>
                                <li><strong>أو جرب على جهاز آخر تماماً</strong></li>
                                <li><strong>حاول استخدام نفس كود التفعيل</strong></li>
                            </ol>
                            <p style="color: #b45309; font-weight: 600;">
                                النتيجة المتوقعة: ❌ <strong>سيرفض النظام الكود</strong>
                            </p>
                        </div>

                        <div style="background: #fee2e2; border: 1px solid #fecaca; border-radius: 8px; padding: 20px; margin: 20px 0;">
                            <h4 style="color: #991b1b; margin-bottom: 15px;">🛡️ مستويات الحماية:</h4>
                            <ul style="color: #b91c1c; line-height: 1.8; padding-right: 20px;">
                                <li><strong>بصمة المتصفح:</strong> نوع وإصدار المتصفح</li>
                                <li><strong>دقة الشاشة:</strong> أبعاد الشاشة الفريدة</li>
                                <li><strong>المنطقة الزمنية:</strong> التوقيت المحلي</li>
                                <li><strong>بصمة Canvas:</strong> رسم فريد لا يمكن تقليده</li>
                                <li><strong>معلومات النظام:</strong> خصائص الجهاز</li>
                            </ul>
                        </div>

                        <div style="background: #d1fae5; border: 1px solid #a7f3d0; border-radius: 8px; padding: 20px; margin: 20px 0;">
                            <h4 style="color: #065f46; margin-bottom: 15px;">✅ ضمانات الأمان:</h4>
                            <ul style="color: #047857; line-height: 1.8; padding-right: 20px;">
                                <li><strong>ربط صارم بالجهاز:</strong> لا يمكن نقل الترخيص</li>
                                <li><strong>تشفير متقدم:</strong> لا يمكن تزوير الأكواد</li>
                                <li><strong>توقيع رقمي:</strong> التحقق من صحة الكود</li>
                                <li><strong>فحص مستمر:</strong> التحقق عند كل استخدام</li>
                                <li><strong>حماية من التلاعب:</strong> كشف المحاولات المشبوهة</li>
                            </ul>
                        </div>

                        <div style="background: #eff6ff; border: 1px solid #bfdbfe; border-radius: 8px; padding: 20px;">
                            <h4 style="color: #1e40af; margin-bottom: 15px;">📋 تقرير الاختبار:</h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; color: #1e40af;">
                                <div>
                                    <strong>الجهاز الحالي:</strong><br>
                                    <span style="color: #059669;">✅ مسموح</span>
                                </div>
                                <div>
                                    <strong>أجهزة أخرى:</strong><br>
                                    <span style="color: #dc2626;">❌ مرفوض</span>
                                </div>
                                <div>
                                    <strong>متصفحات أخرى:</strong><br>
                                    <span style="color: #dc2626;">❌ مرفوض</span>
                                </div>
                                <div>
                                    <strong>نوافذ خفية:</strong><br>
                                    <span style="color: #dc2626;">❌ مرفوض</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">إغلاق</button>
                        <button class="btn btn-primary" onclick="copyDeviceCode('${currentUserCode}')">
                            <i class="fas fa-copy"></i>
                            نسخ كود المستخدم
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // تسجيل الخروج من النظام
        function logoutFromSystem() {
            if (confirm('هل تريد تسجيل الخروج من النظام؟\n\nسيتم حذف جميع البيانات المحلية والعودة لصفحة تسجيل الدخول.')) {

                // حذف جميع البيانات المحلية
                const keysToRemove = [
                    'license',
                    'license_activated',
                    'usage_count',
                    'first_use',
                    'user_email',
                    'user_session',
                    'remember_me',
                    'organization_settings'
                ];

                keysToRemove.forEach(key => {
                    localStorage.removeItem(key);
                    sessionStorage.removeItem(key);
                });

                // حذف بيانات الاستخدام والمراسلات (اختياري)
                if (confirm('هل تريد أيضاً حذف جميع المراسلات والبيانات المحفوظة؟\n\n(اختر "إلغاء" للاحتفاظ بالمراسلات)')) {
                    Object.keys(localStorage).forEach(key => {
                        if (key.startsWith('correspondence_') || key.startsWith('files_') || key.startsWith('usage_')) {
                            localStorage.removeItem(key);
                        }
                    });
                }

                // إظهار رسالة تأكيد
                alert('تم تسجيل الخروج بنجاح!\n\nسيتم إعادة تحميل الصفحة.');

                // إعادة تحميل الصفحة بعد ثانيتين
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            }
        }
    </script>
</body>
</html>
