<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الحماية المتقدم - نسخة المطور v2.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .card h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .btn-success {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, #f44336 0%, #da190b 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        }

        .btn-info {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        }

        .result-box {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            word-break: break-all;
            max-height: 200px;
            overflow-y: auto;
        }

        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .license-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .license-table th,
        .license-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e1e8ed;
        }

        .license-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .status-active {
            color: #28a745;
            font-weight: 600;
        }

        .status-expired {
            color: #dc3545;
            font-weight: 600;
        }

        .security-level {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .security-maximum {
            background: #dc3545;
            color: white;
        }

        .security-high {
            background: #fd7e14;
            color: white;
        }

        .security-normal {
            background: #28a745;
            color: white;
        }

        .tools-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .tool-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            border: 2px solid #e1e8ed;
            transition: all 0.3s ease;
        }

        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .tool-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #667eea;
        }

        .advanced-features {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
            text-align: center;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .feature-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .feature-item i {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-shield-alt"></i> نظام الحماية المتقدم</h1>
            <p>نسخة المطور v2.0 - حماية متعددة الطبقات مع تشفير AES-256</p>
        </div>

        <!-- Dashboard -->
        <div class="dashboard">
            <!-- إحصائيات متقدمة -->
            <div class="card">
                <h3><i class="fas fa-chart-bar"></i> الإحصائيات المتقدمة</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number" id="total-licenses">0</div>
                        <div class="stat-label">إجمالي التراخيص</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="active-licenses">0</div>
                        <div class="stat-label">التراخيص النشطة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="expired-licenses">0</div>
                        <div class="stat-label">التراخيص المنتهية</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="today-licenses">0</div>
                        <div class="stat-label">تراخيص اليوم</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="month-licenses">0</div>
                        <div class="stat-label">تراخيص الشهر</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="maximum-security">0</div>
                        <div class="stat-label">حماية قصوى</div>
                    </div>
                </div>
            </div>

            <!-- توليد كود التفعيل المتقدم -->
            <div class="card">
                <h3><i class="fas fa-key"></i> توليد كود التفعيل المتقدم</h3>
                <div class="form-group">
                    <label>كود المستخدم المتقدم:</label>
                    <textarea id="advanced-user-code" rows="4" placeholder="الصق كود المستخدم المتقدم هنا..."></textarea>
                </div>
                <div class="form-group">
                    <label>مدة الصلاحية:</label>
                    <select id="advanced-expiry-days">
                        <option value="30">30 يوم (تجربة مدفوعة)</option>
                        <option value="90">90 يوم (ربع سنوي)</option>
                        <option value="180">180 يوم (نصف سنوي)</option>
                        <option value="365" selected>365 يوم (سنوي)</option>
                        <option value="730">730 يوم (سنتان)</option>
                        <option value="1095">1095 يوم (3 سنوات)</option>
                        <option value="0">بدون انتهاء (مدى الحياة)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>ملاحظات:</label>
                    <input type="text" id="advanced-notes" placeholder="اسم العميل، نوع الترخيص، إلخ...">
                </div>
                <button class="btn" onclick="generateAdvancedActivationCode()">
                    <i class="fas fa-magic"></i> توليد كود التفعيل المتقدم
                </button>
                <button class="btn btn-success" onclick="generateQuickAdvancedLicense()">
                    <i class="fas fa-bolt"></i> إنشاء ترخيص سريع للاختبار
                </button>
                <div id="advanced-activation-result" class="result-box" style="display: none;"></div>
            </div>

            <!-- التحقق من كود التفعيل المتقدم -->
            <div class="card">
                <h3><i class="fas fa-search"></i> التحقق من كود التفعيل المتقدم</h3>
                <div class="form-group">
                    <label>كود التفعيل المتقدم:</label>
                    <textarea id="verify-advanced-code" rows="4" placeholder="الصق كود التفعيل المتقدم للتحقق منه..."></textarea>
                </div>
                <button class="btn btn-info" onclick="verifyAdvancedActivationCode()">
                    <i class="fas fa-check-circle"></i> التحقق من الكود المتقدم
                </button>
                <div id="advanced-verify-result" class="result-box" style="display: none;"></div>
            </div>
        </div>

        <!-- سجل التراخيص المتقدمة -->
        <div class="card">
            <h3><i class="fas fa-list"></i> سجل التراخيص المتقدمة</h3>
            <button class="btn btn-success" onclick="refreshAdvancedLicenses()">
                <i class="fas fa-sync"></i> تحديث السجل
            </button>
            <button class="btn btn-info" onclick="exportAdvancedLicenses()">
                <i class="fas fa-download"></i> تصدير السجل المتقدم
            </button>
            <div id="advanced-licenses-table"></div>
        </div>

        <!-- أدوات الصيانة المتقدمة -->
        <div class="tools-section">
            <h3><i class="fas fa-tools"></i> أدوات الصيانة المتقدمة</h3>
            <div class="tools-grid">
                <div class="tool-card">
                    <div class="tool-icon"><i class="fas fa-broom"></i></div>
                    <h4>تنظيف التراخيص المنتهية</h4>
                    <p>حذف التراخيص المنتهية الصلاحية</p>
                    <button class="btn btn-warning" onclick="cleanExpiredAdvancedLicenses()">تنظيف</button>
                </div>
                
                <div class="tool-card">
                    <div class="tool-icon"><i class="fas fa-save"></i></div>
                    <h4>نسخ احتياطي متقدم</h4>
                    <p>حفظ جميع بيانات التراخيص المتقدمة</p>
                    <button class="btn btn-success" onclick="backupAdvancedData()">نسخ احتياطي</button>
                </div>
                
                <div class="tool-card">
                    <div class="tool-icon"><i class="fas fa-vial"></i></div>
                    <h4>اختبار النظام المتقدم</h4>
                    <p>فحص جميع وظائف النظام المتقدم</p>
                    <button class="btn btn-info" onclick="testAdvancedSystem()">اختبار</button>
                </div>
                
                <div class="tool-card">
                    <div class="tool-icon"><i class="fas fa-exclamation-triangle"></i></div>
                    <h4>إعادة تعيين شاملة</h4>
                    <p>حذف جميع البيانات نهائياً</p>
                    <button class="btn btn-danger" onclick="resetAdvancedSystem()">إعادة تعيين</button>
                </div>
            </div>
        </div>

        <!-- المميزات المتقدمة -->
        <div class="advanced-features">
            <h3><i class="fas fa-star"></i> مميزات النظام المتقدم v2.0</h3>
            <div class="features-grid">
                <div class="feature-item">
                    <i class="fas fa-lock"></i>
                    <h4>تشفير AES-256</h4>
                    <p>تشفير عسكري المستوى</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-desktop"></i>
                    <h4>كشف الآلات الافتراضية</h4>
                    <p>منع الاستخدام في البيئات الوهمية</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-mobile-alt"></i>
                    <h4>كشف المحاكيات</h4>
                    <p>حماية من محاكيات الأجهزة</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-mouse"></i>
                    <h4>تتبع السلوك</h4>
                    <p>تحليل أنماط استخدام المستخدم</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-heartbeat"></i>
                    <h4>نبضة القلب</h4>
                    <p>اتصال دوري مع الخادم</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-shield-virus"></i>
                    <h4>حماية من التلاعب</h4>
                    <p>كشف محاولات تعديل الكود</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-fingerprint"></i>
                    <h4>بصمة متقدمة</h4>
                    <p>تحديد فريد للجهاز</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-signature"></i>
                    <h4>التوقيع الرقمي</h4>
                    <p>ضمان صحة التراخيص</p>
                </div>
            </div>
        </div>
    </div>

    <!-- تضمين النظام المتقدم -->
    <script src="js/advanced-license.js"></script>
    
    <script>
        // تحديث الإحصائيات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateAdvancedStats();
            loadAdvancedLicensesTable();
        });

        // تحديث الإحصائيات المتقدمة
        function updateAdvancedStats() {
            const stats = JSON.parse(localStorage.getItem('advanced_license_stats') || '{}');
            
            document.getElementById('total-licenses').textContent = stats.total || 0;
            document.getElementById('active-licenses').textContent = stats.active || 0;
            document.getElementById('expired-licenses').textContent = stats.expired || 0;
            document.getElementById('today-licenses').textContent = stats.today || 0;
            document.getElementById('month-licenses').textContent = stats.thisMonth || 0;
            document.getElementById('maximum-security').textContent = stats.securityLevels?.maximum || 0;
        }

        // توليد كود التفعيل المتقدم
        async function generateAdvancedActivationCode() {
            const userCode = document.getElementById('advanced-user-code').value.trim();
            const expiryDays = parseInt(document.getElementById('advanced-expiry-days').value);
            const notes = document.getElementById('advanced-notes').value.trim();
            
            if (!userCode) {
                alert('يرجى إدخال كود المستخدم المتقدم');
                return;
            }
            
            try {
                const activationCode = await advancedLicenseManager.generateAdvancedActivationCode(userCode, expiryDays, notes);
                
                const resultDiv = document.getElementById('advanced-activation-result');
                if (activationCode) {
                    resultDiv.className = 'result-box success';
                    resultDiv.innerHTML = `
                        <h4>✅ تم توليد كود التفعيل المتقدم بنجاح!</h4>
                        <p><strong>كود التفعيل:</strong></p>
                        <div style="background: #fff; padding: 10px; border-radius: 5px; margin: 10px 0; word-break: break-all; color: #333;">
                            ${activationCode}
                        </div>
                        <button onclick="navigator.clipboard.writeText('${activationCode}').then(() => alert('تم نسخ الكود!'))" 
                                style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">
                            📋 نسخ الكود
                        </button>
                    `;
                } else {
                    resultDiv.className = 'result-box error';
                    resultDiv.innerHTML = '<h4>❌ فشل في توليد كود التفعيل</h4><p>تحقق من صحة كود المستخدم</p>';
                }
                resultDiv.style.display = 'block';
                
                // تحديث الإحصائيات والجدول
                updateAdvancedStats();
                loadAdvancedLicensesTable();
                
            } catch (error) {
                const resultDiv = document.getElementById('advanced-activation-result');
                resultDiv.className = 'result-box error';
                resultDiv.innerHTML = `<h4>❌ خطأ في التوليد</h4><p>${error.message}</p>`;
                resultDiv.style.display = 'block';
            }
        }

        // التحقق من كود التفعيل المتقدم
        async function verifyAdvancedActivationCode() {
            const code = document.getElementById('verify-advanced-code').value.trim();
            
            if (!code) {
                alert('يرجى إدخال كود التفعيل للتحقق منه');
                return;
            }
            
            try {
                const decryptedData = await advancedLicenseManager.decryptAES256(code);
                const licenseData = JSON.parse(decryptedData);
                
                const isValid = await advancedLicenseManager.validateAdvancedLicense(licenseData);
                const resultDiv = document.getElementById('advanced-verify-result');
                
                if (isValid) {
                    const expiryDate = licenseData.expiryDate ? new Date(licenseData.expiryDate) : null;
                    const daysRemaining = expiryDate ? Math.ceil((expiryDate - new Date()) / (24 * 60 * 60 * 1000)) : null;
                    
                    resultDiv.className = 'result-box success';
                    resultDiv.innerHTML = `
                        <h4>✅ كود التفعيل صحيح</h4>
                        <p><strong>تاريخ الإصدار:</strong> ${new Date(licenseData.issuedDate).toLocaleDateString('ar-SA')}</p>
                        <p><strong>تاريخ الانتهاء:</strong> ${expiryDate ? expiryDate.toLocaleDateString('ar-SA') : 'بدون انتهاء'}</p>
                        <p><strong>الأيام المتبقية:</strong> ${daysRemaining !== null ? daysRemaining + ' يوم' : 'غير محدود'}</p>
                        <p><strong>مستوى الأمان:</strong> <span class="security-level security-${licenseData.securityLevel.toLowerCase()}">${licenseData.securityLevel}</span></p>
                        <p><strong>الملاحظات:</strong> ${licenseData.notes || 'لا توجد ملاحظات'}</p>
                    `;
                } else {
                    resultDiv.className = 'result-box error';
                    resultDiv.innerHTML = '<h4>❌ كود التفعيل غير صحيح أو منتهي الصلاحية</h4>';
                }
                resultDiv.style.display = 'block';
                
            } catch (error) {
                const resultDiv = document.getElementById('advanced-verify-result');
                resultDiv.className = 'result-box error';
                resultDiv.innerHTML = `<h4>❌ خطأ في التحقق</h4><p>${error.message}</p>`;
                resultDiv.style.display = 'block';
            }
        }

        // إنشاء ترخيص سريع للاختبار
        async function generateQuickAdvancedLicense() {
            try {
                // إنشاء كود مستخدم وهمي للاختبار
                const testUserCode = await advancedLicenseManager.generateAdvancedUserCode();

                // توليد كود التفعيل
                const activationCode = await advancedLicenseManager.generateAdvancedActivationCode(testUserCode, 365, 'ترخيص تجريبي سريع');

                const resultDiv = document.getElementById('advanced-activation-result');
                if (activationCode) {
                    resultDiv.className = 'result-box success';
                    resultDiv.innerHTML = `
                        <h4>✅ تم إنشاء ترخيص تجريبي سريع بنجاح!</h4>
                        <p><strong>كود التفعيل المتقدم:</strong></p>
                        <div style="background: #fff; padding: 10px; border-radius: 5px; margin: 10px 0; word-break: break-all; color: #333;">
                            ${activationCode}
                        </div>
                        <button onclick="navigator.clipboard.writeText('${activationCode}').then(() => alert('تم نسخ الكود!'))"
                                style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; margin: 5px;">
                            📋 نسخ الكود
                        </button>
                        <div style="background: #e7f3ff; padding: 10px; border-radius: 5px; margin-top: 10px; color: #0066cc; font-size: 0.9rem;">
                            <strong>ملاحظة:</strong> هذا ترخيص تجريبي صالح لمدة سنة كاملة مع جميع المميزات المتقدمة
                        </div>
                    `;
                } else {
                    resultDiv.className = 'result-box error';
                    resultDiv.innerHTML = '<h4>❌ فشل في إنشاء الترخيص التجريبي</h4>';
                }
                resultDiv.style.display = 'block';

                // تحديث الإحصائيات والجدول
                updateAdvancedStats();
                loadAdvancedLicensesTable();

            } catch (error) {
                const resultDiv = document.getElementById('advanced-activation-result');
                resultDiv.className = 'result-box error';
                resultDiv.innerHTML = `<h4>❌ خطأ في الإنشاء</h4><p>${error.message}</p>`;
                resultDiv.style.display = 'block';
            }
        }

        // تحديث سجل التراخيص المتقدمة
        function refreshAdvancedLicenses() {
            loadAdvancedLicensesTable();
            updateAdvancedStats();
            alert('تم تحديث السجل بنجاح!');
        }

        // تحميل جدول التراخيص المتقدمة
        function loadAdvancedLicensesTable() {
            const licenses = JSON.parse(localStorage.getItem('advanced_developer_licenses') || '[]');
            const tableContainer = document.getElementById('advanced-licenses-table');

            if (licenses.length === 0) {
                tableContainer.innerHTML = '<p style="text-align: center; color: #6c757d; margin: 20px 0;">لا توجد تراخيص متقدمة حتى الآن</p>';
                return;
            }

            let tableHTML = `
                <table class="license-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>معرف الجهاز</th>
                            <th>تاريخ الإصدار</th>
                            <th>تاريخ الانتهاء</th>
                            <th>مستوى الأمان</th>
                            <th>الحالة</th>
                            <th>الملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            licenses.forEach(license => {
                const isExpired = license.expiryDate && new Date(license.expiryDate) <= new Date();
                const statusClass = isExpired ? 'status-expired' : 'status-active';
                const statusText = isExpired ? 'منتهي' : 'نشط';

                tableHTML += `
                    <tr>
                        <td>${license.id}</td>
                        <td>${license.deviceId}</td>
                        <td>${new Date(license.issuedDate).toLocaleDateString('ar-SA')}</td>
                        <td>${license.expiryDate ? new Date(license.expiryDate).toLocaleDateString('ar-SA') : 'بدون انتهاء'}</td>
                        <td><span class="security-level security-${license.securityLevel.toLowerCase()}">${license.securityLevel}</span></td>
                        <td><span class="${statusClass}">${statusText}</span></td>
                        <td>${license.notes || '-'}</td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table>';
            tableContainer.innerHTML = tableHTML;
        }

        // تصدير التراخيص المتقدمة
        function exportAdvancedLicenses() {
            const licenses = JSON.parse(localStorage.getItem('advanced_developer_licenses') || '[]');
            const stats = JSON.parse(localStorage.getItem('advanced_license_stats') || '{}');

            const exportData = {
                exportDate: new Date().toISOString(),
                version: '2.0.0',
                systemType: 'ADVANCED_LICENSE_SYSTEM',
                statistics: stats,
                licenses: licenses,
                totalCount: licenses.length
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `advanced_licenses_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            alert('تم تصدير سجل التراخيص المتقدمة بنجاح!');
        }

        // تنظيف التراخيص المنتهية
        function cleanExpiredAdvancedLicenses() {
            if (!confirm('هل أنت متأكد من حذف جميع التراخيص المنتهية الصلاحية؟')) {
                return;
            }

            const licenses = JSON.parse(localStorage.getItem('advanced_developer_licenses') || '[]');
            const now = new Date();

            const activeLicenses = licenses.filter(license => {
                if (!license.expiryDate) return true; // التراخيص الدائمة
                return new Date(license.expiryDate) > now;
            });

            const removedCount = licenses.length - activeLicenses.length;

            localStorage.setItem('advanced_developer_licenses', JSON.stringify(activeLicenses));
            advancedLicenseManager.updateAdvancedStats();

            loadAdvancedLicensesTable();
            updateAdvancedStats();

            alert(`تم حذف ${removedCount} ترخيص منتهي الصلاحية`);
        }

        // نسخ احتياطي للبيانات المتقدمة
        function backupAdvancedData() {
            const allData = {
                licenses: JSON.parse(localStorage.getItem('advanced_developer_licenses') || '[]'),
                stats: JSON.parse(localStorage.getItem('advanced_license_stats') || '{}'),
                backupDate: new Date().toISOString(),
                version: '2.0.0',
                systemType: 'ADVANCED_LICENSE_SYSTEM_BACKUP'
            };

            const dataStr = JSON.stringify(allData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `advanced_backup_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            alert('تم إنشاء النسخة الاحتياطية المتقدمة بنجاح!');
        }

        // اختبار النظام المتقدم
        async function testAdvancedSystem() {
            const results = [];

            try {
                // اختبار توليد بصمة الجهاز
                const deviceFingerprint = await advancedLicenseManager.getAdvancedDeviceFingerprint();
                results.push(`✅ بصمة الجهاز: ${deviceFingerprint.substring(0, 20)}...`);

                // اختبار توليد كود المستخدم
                const userCode = await advancedLicenseManager.generateAdvancedUserCode();
                results.push(`✅ كود المستخدم: ${userCode.substring(0, 30)}...`);

                // اختبار التشفير وفك التشفير
                const testData = 'test_encryption_data';
                const encrypted = await advancedLicenseManager.encryptAES256(testData);
                const decrypted = await advancedLicenseManager.decryptAES256(encrypted);
                results.push(`✅ التشفير: ${decrypted === testData ? 'نجح' : 'فشل'}`);

                // اختبار كشف البيئة
                const isVM = advancedLicenseManager.detectVirtualMachine();
                const isEmulator = advancedLicenseManager.detectEmulator();
                const isTampered = advancedLicenseManager.detectTampering();
                results.push(`✅ كشف الآلة الافتراضية: ${isVM ? 'تم الكشف' : 'غير موجودة'}`);
                results.push(`✅ كشف المحاكي: ${isEmulator ? 'تم الكشف' : 'غير موجود'}`);
                results.push(`✅ كشف التلاعب: ${isTampered ? 'تم الكشف' : 'غير موجود'}`);

                // اختبار البصمات المتقدمة
                const canvasFingerprint = advancedLicenseManager.getCanvasFingerprint();
                const webglFingerprint = advancedLicenseManager.getWebGLFingerprint();
                const audioFingerprint = advancedLicenseManager.getAudioFingerprint();
                results.push(`✅ بصمة Canvas: ${canvasFingerprint.substring(0, 20)}...`);
                results.push(`✅ بصمة WebGL: ${webglFingerprint.substring(0, 20)}...`);
                results.push(`✅ بصمة الصوت: ${audioFingerprint.substring(0, 20)}...`);

                alert('نتائج اختبار النظام المتقدم:\n\n' + results.join('\n'));

            } catch (error) {
                alert('خطأ في اختبار النظام: ' + error.message);
            }
        }

        // إعادة تعيين النظام المتقدم
        function resetAdvancedSystem() {
            if (!confirm('⚠️ تحذير: هذا سيحذف جميع التراخيص والبيانات نهائياً!\n\nهل أنت متأكد؟')) {
                return;
            }

            if (!confirm('تأكيد أخير: هل تريد حقاً حذف جميع بيانات النظام المتقدم؟')) {
                return;
            }

            // حذف جميع البيانات المتقدمة
            localStorage.removeItem('advanced_developer_licenses');
            localStorage.removeItem('advanced_license_stats');
            localStorage.removeItem('advanced_license');
            localStorage.removeItem('advanced_license_activated');

            // حذف بيانات الاستخدام
            Object.keys(localStorage).forEach(key => {
                if (key.startsWith('advanced_usage_')) {
                    localStorage.removeItem(key);
                }
            });

            // تحديث الواجهة
            updateAdvancedStats();
            loadAdvancedLicensesTable();

            alert('تم إعادة تعيين النظام المتقدم بالكامل!');
        }

        // إنشاء ترخيص تجريبي متقدم
        async function generateAdvancedTestLicense() {
            try {
                const userCode = await advancedLicenseManager.generateAdvancedUserCode();
                const activationCode = await advancedLicenseManager.generateAdvancedActivationCode(userCode, 365, 'ترخيص تجريبي متقدم');

                if (activationCode) {
                    const success = await advancedLicenseManager.activateAdvancedLicense(activationCode);

                    if (success) {
                        alert('تم إنشاء وتطبيق ترخيص تجريبي متقدم بنجاح!\nصالح لمدة سنة كاملة مع جميع المميزات المتقدمة.');
                        updateAdvancedStats();
                        loadAdvancedLicensesTable();
                    } else {
                        alert('فشل في تطبيق الترخيص التجريبي المتقدم');
                    }
                } else {
                    alert('فشل في إنشاء الترخيص التجريبي المتقدم');
                }
            } catch (error) {
                alert('خطأ في إنشاء الترخيص التجريبي: ' + error.message);
            }
        }

        // إضافة أزرار إضافية للاختبار
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة زر الترخيص التجريبي
            const toolsGrid = document.querySelector('.tools-grid');
            const testLicenseCard = document.createElement('div');
            testLicenseCard.className = 'tool-card';
            testLicenseCard.innerHTML = `
                <div class="tool-icon"><i class="fas fa-flask"></i></div>
                <h4>ترخيص تجريبي متقدم</h4>
                <p>إنشاء ترخيص تجريبي للاختبار</p>
                <button class="btn btn-success" onclick="generateAdvancedTestLicense()">إنشاء ترخيص</button>
            `;
            toolsGrid.appendChild(testLicenseCard);
        });
    </script>
</body>
</html>
