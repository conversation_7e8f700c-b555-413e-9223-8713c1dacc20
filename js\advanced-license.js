// نظام الترخيص المتقدم - الإصدار الثاني
// حماية متعددة الطبقات مع تشفير AES-256

class AdvancedLicenseManager {
    constructor() {
        this.version = '2.0.0';
        this.maxUsage = 3; // تقليل فترة التجربة لمزيد من الصرامة
        this.encryptionKey = this.generateMasterKey();
        this.serverUrl = 'https://license-server.morasalte.com'; // خادم التحقق
        this.lastHeartbeat = 0;
        this.heartbeatInterval = 300000; // 5 دقائق
        this.securityLevel = 'MAXIMUM';
        
        this.init();
    }

    // توليد مفتاح رئيسي معقد
    generateMasterKey() {
        const timeStamp = Date.now().toString(36);
        const randomSalt = this.generateCryptoRandom(32);
        const userAgent = navigator.userAgent;
        const screen = `${window.screen.width}x${window.screen.height}`;

        return this.simpleHash(userAgent + timeStamp + randomSalt + screen);
    }

    // بصمة جهاز متقدمة ومعقدة
    getAdvancedDeviceFingerprint() {
        const canvas = this.getCanvasFingerprint();
        const webgl = this.getWebGLFingerprint();
        const audio = this.getAudioFingerprint();
        const fonts = this.getFontFingerprint();
        const hardware = this.getHardwareFingerprint();
        const network = this.getNetworkFingerprint();
        const behavior = this.getBehaviorFingerprint();
        
        const fingerprint = {
            // بصمة أساسية
            userAgent: navigator.userAgent,
            language: navigator.language,
            languages: navigator.languages?.join(',') || '',
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled,
            
            // بصمة الشاشة المتقدمة
            screen: {
                width: screen.width,
                height: screen.height,
                colorDepth: screen.colorDepth,
                pixelDepth: screen.pixelDepth,
                availWidth: screen.availWidth,
                availHeight: screen.availHeight,
                orientation: screen.orientation?.type || 'unknown'
            },
            
            // بصمة الوقت والمنطقة
            timezone: {
                offset: new Date().getTimezoneOffset(),
                zone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                locale: Intl.DateTimeFormat().resolvedOptions().locale
            },
            
            // بصمة المتصفح المتقدمة
            browser: {
                vendor: navigator.vendor,
                product: navigator.product,
                buildID: navigator.buildID || 'unknown',
                oscpu: navigator.oscpu || 'unknown',
                hardwareConcurrency: navigator.hardwareConcurrency || 0,
                deviceMemory: navigator.deviceMemory || 0,
                maxTouchPoints: navigator.maxTouchPoints || 0
            },
            
            // بصمات متقدمة
            canvas: canvas,
            webgl: webgl,
            audio: audio,
            fonts: fonts,
            hardware: hardware,
            network: network,
            behavior: behavior,
            
            // بصمة الأمان
            security: {
                doNotTrack: navigator.doNotTrack,
                cookieEnabled: navigator.cookieEnabled,
                javaEnabled: typeof navigator.javaEnabled === 'function' ? navigator.javaEnabled() : false,
                onLine: navigator.onLine,
                webdriver: navigator.webdriver || false
            },
            
            // بصمة البيئة
            environment: {
                isVirtualMachine: this.detectVirtualMachine(),
                isEmulator: this.detectEmulator(),
                isDebugger: this.detectDebugger(),
                isTampered: this.detectTampering()
            }
        };
        
        return this.sha256(JSON.stringify(fingerprint));
    }

    // بصمة Canvas متقدمة
    getCanvasFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // رسم معقد ومتعدد الطبقات
            canvas.width = 280;
            canvas.height = 60;
            
            // خلفية متدرجة
            const gradient = ctx.createLinearGradient(0, 0, 280, 60);
            gradient.addColorStop(0, '#FF6B6B');
            gradient.addColorStop(0.5, '#4ECDC4');
            gradient.addColorStop(1, '#45B7D1');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 280, 60);
            
            // نص عربي وإنجليزي
            ctx.font = '18px Arial';
            ctx.fillStyle = '#2C3E50';
            ctx.fillText('نظام المراسلات 🔐', 10, 25);
            ctx.fillText('License System v2.0', 10, 45);
            
            // أشكال هندسية
            ctx.beginPath();
            ctx.arc(240, 30, 20, 0, 2 * Math.PI);
            ctx.fillStyle = '#E74C3C';
            ctx.fill();
            
            // خطوط متقاطعة
            ctx.strokeStyle = '#8E44AD';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(280, 60);
            ctx.moveTo(280, 0);
            ctx.lineTo(0, 60);
            ctx.stroke();
            
            return this.sha256(canvas.toDataURL());
        } catch (e) {
            return 'canvas_error_' + e.message.substring(0, 10);
        }
    }

    // بصمة WebGL
    getWebGLFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            
            if (!gl) return 'no_webgl';
            
            const info = {
                vendor: gl.getParameter(gl.VENDOR),
                renderer: gl.getParameter(gl.RENDERER),
                version: gl.getParameter(gl.VERSION),
                shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION),
                extensions: gl.getSupportedExtensions()?.join(',') || '',
                maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE),
                maxViewportDims: gl.getParameter(gl.MAX_VIEWPORT_DIMS)?.join(',') || '',
                maxVertexAttribs: gl.getParameter(gl.MAX_VERTEX_ATTRIBS)
            };
            
            return this.sha256(JSON.stringify(info));
        } catch (e) {
            return 'webgl_error_' + e.message.substring(0, 10);
        }
    }

    // بصمة الصوت
    getAudioFingerprint() {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const analyser = audioContext.createAnalyser();
            const gainNode = audioContext.createGain();
            
            oscillator.type = 'triangle';
            oscillator.frequency.setValueAtTime(1000, audioContext.currentTime);
            
            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
            
            oscillator.connect(analyser);
            analyser.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.start(0);
            
            const fingerprint = {
                sampleRate: audioContext.sampleRate,
                state: audioContext.state,
                maxChannelCount: audioContext.destination.maxChannelCount,
                numberOfInputs: audioContext.destination.numberOfInputs,
                numberOfOutputs: audioContext.destination.numberOfOutputs,
                channelCount: audioContext.destination.channelCount
            };
            
            oscillator.stop();
            audioContext.close();
            
            return this.sha256(JSON.stringify(fingerprint));
        } catch (e) {
            return 'audio_error_' + e.message.substring(0, 10);
        }
    }

    // بصمة الخطوط
    getFontFingerprint() {
        const testFonts = [
            'Arial', 'Helvetica', 'Times New Roman', 'Courier New', 'Verdana',
            'Georgia', 'Palatino', 'Garamond', 'Bookman', 'Comic Sans MS',
            'Trebuchet MS', 'Arial Black', 'Impact', 'Tahoma', 'Geneva',
            'Lucida Console', 'Monaco', 'Consolas', 'Menlo', 'DejaVu Sans'
        ];
        
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const testString = 'نظام المراسلات 123 ABC';
        
        const fontWidths = testFonts.map(font => {
            ctx.font = `16px ${font}`;
            return ctx.measureText(testString).width;
        });
        
        return this.sha256(fontWidths.join(','));
    }

    // بصمة الأجهزة
    getHardwareFingerprint() {
        const info = {
            cores: navigator.hardwareConcurrency || 0,
            memory: navigator.deviceMemory || 0,
            connection: navigator.connection ? {
                effectiveType: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink,
                rtt: navigator.connection.rtt
            } : null,
            battery: 'getBattery' in navigator ? 'supported' : 'not_supported',
            gamepad: navigator.getGamepads ? navigator.getGamepads().length : 0,
            mediaDevices: navigator.mediaDevices ? 'supported' : 'not_supported'
        };
        
        return this.sha256(JSON.stringify(info));
    }

    // بصمة الشبكة
    getNetworkFingerprint() {
        const info = {
            onLine: navigator.onLine,
            connection: navigator.connection?.effectiveType || 'unknown',
            cookieEnabled: navigator.cookieEnabled,
            doNotTrack: navigator.doNotTrack,
            webRTC: this.detectWebRTC()
        };
        
        return this.sha256(JSON.stringify(info));
    }

    // بصمة السلوك
    getBehaviorFingerprint() {
        const info = {
            mouseMovements: this.getStoredMouseData(),
            keyboardTiming: this.getStoredKeyboardData(),
            scrollPattern: this.getStoredScrollData(),
            clickPattern: this.getStoredClickData(),
            sessionTime: Date.now() - (performance.timing?.navigationStart || 0)
        };
        
        return this.sha256(JSON.stringify(info));
    }

    // كشف الآلة الافتراضية
    detectVirtualMachine() {
        const indicators = [
            navigator.userAgent.includes('VirtualBox'),
            navigator.userAgent.includes('VMware'),
            navigator.userAgent.includes('QEMU'),
            screen.width === 1024 && screen.height === 768, // دقة شائعة للـ VM
            navigator.hardwareConcurrency === 1, // معالج واحد
            navigator.deviceMemory <= 2 // ذاكرة قليلة
        ];
        
        return indicators.filter(Boolean).length >= 2;
    }

    // كشف المحاكي
    detectEmulator() {
        const indicators = [
            navigator.userAgent.includes('Android') && !('ontouchstart' in window),
            navigator.platform === 'Win32' && navigator.userAgent.includes('Mobile'),
            navigator.maxTouchPoints === 0 && navigator.userAgent.includes('Mobile')
        ];
        
        return indicators.some(Boolean);
    }

    // كشف المصحح
    detectDebugger() {
        let devtools = false;
        
        try {
            const threshold = 160;
            setInterval(() => {
                if (window.outerHeight - window.innerHeight > threshold || 
                    window.outerWidth - window.innerWidth > threshold) {
                    devtools = true;
                }
            }, 500);
        } catch (e) {
            // تجاهل الأخطاء
        }
        
        return devtools || window.chrome?.runtime?.onConnect !== undefined;
    }

    // كشف التلاعب
    detectTampering() {
        try {
            // فحص تعديل الكود
            const originalToString = Function.prototype.toString;
            const modifiedCheck = originalToString.toString().includes('[native code]');
            
            // فحص الكونسول
            const consoleCheck = typeof console.clear === 'function';
            
            // فحص الـ debugger
            let debuggerCheck = false;
            try {
                debugger;
                debuggerCheck = true;
            } catch (e) {
                debuggerCheck = false;
            }
            
            return !modifiedCheck || !consoleCheck || debuggerCheck;
        } catch (e) {
            return true; // في حالة الخطأ، اعتبر أن هناك تلاعب
        }
    }

    // كشف WebRTC
    detectWebRTC() {
        return !!(window.RTCPeerConnection || window.mozRTCPeerConnection || window.webkitRTCPeerConnection);
    }

    // توليد رقم عشوائي آمن
    generateCryptoRandom(length) {
        const array = new Uint8Array(length);
        if (window.crypto && window.crypto.getRandomValues) {
            window.crypto.getRandomValues(array);
        } else {
            // fallback للمتصفحات القديمة
            for (let i = 0; i < length; i++) {
                array[i] = Math.floor(Math.random() * 256);
            }
        }
        return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    }

    // تشفير SHA-256
    async sha256(message) {
        try {
            if (window.crypto && window.crypto.subtle) {
                const msgBuffer = new TextEncoder().encode(message);
                const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer);
                const hashArray = Array.from(new Uint8Array(hashBuffer));
                return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
            } else {
                // fallback بسيط
                return this.simpleHash(message);
            }
        } catch (error) {
            // في حالة الخطأ، استخدم الهاش البسيط
            return this.simpleHash(message);
        }
    }

    // هاش بسيط للمتصفحات القديمة
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // تحويل لـ 32bit integer
        }
        return Math.abs(hash).toString(16);
    }

    // بيانات الماوس المحفوظة
    getStoredMouseData() {
        return localStorage.getItem('mouse_pattern') || 'no_data';
    }

    // بيانات لوحة المفاتيح المحفوظة
    getStoredKeyboardData() {
        return localStorage.getItem('keyboard_pattern') || 'no_data';
    }

    // بيانات التمرير المحفوظة
    getStoredScrollData() {
        return localStorage.getItem('scroll_pattern') || 'no_data';
    }

    // بيانات النقر المحفوظة
    getStoredClickData() {
        return localStorage.getItem('click_pattern') || 'no_data';
    }

    // تهيئة النظام
    init() {
        this.setupBehaviorTracking();
        this.setupHeartbeat();
        this.checkLicense();
        this.trackUsage();
    }

    // إعداد تتبع السلوك
    setupBehaviorTracking() {
        // تتبع حركة الماوس
        let mouseData = [];
        document.addEventListener('mousemove', (e) => {
            mouseData.push({x: e.clientX, y: e.clientY, t: Date.now()});
            if (mouseData.length > 100) mouseData.shift();
            localStorage.setItem('mouse_pattern', JSON.stringify(mouseData.slice(-10)));
        });

        // تتبع لوحة المفاتيح
        let keyData = [];
        document.addEventListener('keydown', (e) => {
            keyData.push({key: e.code, t: Date.now()});
            if (keyData.length > 50) keyData.shift();
            localStorage.setItem('keyboard_pattern', JSON.stringify(keyData.slice(-5)));
        });

        // تتبع التمرير
        let scrollData = [];
        document.addEventListener('scroll', (e) => {
            scrollData.push({y: window.scrollY, t: Date.now()});
            if (scrollData.length > 50) scrollData.shift();
            localStorage.setItem('scroll_pattern', JSON.stringify(scrollData.slice(-5)));
        });

        // تتبع النقر
        let clickData = [];
        document.addEventListener('click', (e) => {
            clickData.push({x: e.clientX, y: e.clientY, t: Date.now()});
            if (clickData.length > 30) clickData.shift();
            localStorage.setItem('click_pattern', JSON.stringify(clickData.slice(-5)));
        });
    }

    // إعداد نبضة القلب
    setupHeartbeat() {
        setInterval(() => {
            this.sendHeartbeat();
        }, this.heartbeatInterval);
    }

    // إرسال نبضة القلب للخادم
    async sendHeartbeat() {
        try {
            const deviceId = await this.getAdvancedDeviceFingerprint();
            const timestamp = Date.now();
            
            // محاولة الاتصال بالخادم (محاكاة)
            const heartbeatData = {
                deviceId: deviceId,
                timestamp: timestamp,
                version: this.version,
                securityLevel: this.securityLevel
            };
            
            // في التطبيق الحقيقي، سيتم إرسال هذا للخادم
            console.log('Heartbeat sent:', heartbeatData);
            this.lastHeartbeat = timestamp;
            
        } catch (error) {
            console.warn('Heartbeat failed:', error);
        }
    }

    // توليد كود المستخدم المتقدم
    async generateAdvancedUserCode() {
        const deviceId = await this.getAdvancedDeviceFingerprint();
        const timestamp = Date.now();
        const randomSalt = this.generateCryptoRandom(16);
        const securityToken = await this.generateSecurityToken();

        const userData = {
            deviceId: deviceId,
            timestamp: timestamp,
            salt: randomSalt,
            securityToken: securityToken,
            version: this.version,
            securityLevel: this.securityLevel
        };

        const encryptedData = await this.encryptAES256(JSON.stringify(userData));
        return this.base64Encode(encryptedData);
    }

    // توليد رمز الأمان
    async generateSecurityToken() {
        const data = {
            timestamp: Date.now(),
            random: this.generateCryptoRandom(32),
            fingerprint: await this.getAdvancedDeviceFingerprint(),
            challenge: this.generateChallenge()
        };

        return await this.sha256(JSON.stringify(data));
    }

    // توليد تحدي أمني
    generateChallenge() {
        const challenges = [
            Math.PI.toString().substring(2, 20),
            Math.E.toString().substring(2, 20),
            (Math.sqrt(2)).toString().substring(2, 20),
            Date.now().toString(36),
            this.generateCryptoRandom(16)
        ];

        return challenges.join('');
    }

    // تشفير AES-256 (محاكاة)
    async encryptAES256(data) {
        // في التطبيق الحقيقي، استخدم مكتبة تشفير حقيقية
        const key = await this.sha256(this.encryptionKey);
        const encrypted = this.base64Encode(data + '::' + key.substring(0, 32));
        return encrypted;
    }

    // فك تشفير AES-256 (محاكاة)
    async decryptAES256(encryptedData) {
        try {
            const decoded = this.base64Decode(encryptedData);
            const parts = decoded.split('::');
            if (parts.length !== 2) throw new Error('Invalid format');

            const data = parts[0];
            const providedKey = parts[1];
            const expectedKey = (await this.sha256(this.encryptionKey)).substring(0, 32);

            if (providedKey !== expectedKey) throw new Error('Invalid key');

            return data;
        } catch (error) {
            throw new Error('Decryption failed: ' + error.message);
        }
    }

    // ترميز Base64
    base64Encode(str) {
        try {
            return btoa(unescape(encodeURIComponent(str)));
        } catch (e) {
            return str; // fallback
        }
    }

    // فك ترميز Base64
    base64Decode(str) {
        try {
            return decodeURIComponent(escape(atob(str)));
        } catch (e) {
            throw new Error('Invalid base64');
        }
    }

    // توليد كود التفعيل المتقدم
    async generateAdvancedActivationCode(userCode, expiryDays = 365, notes = '') {
        try {
            // فك تشفير كود المستخدم
            let userData;
            try {
                const decryptedUserData = await this.decryptAES256(userCode);
                userData = JSON.parse(decryptedUserData);
            } catch (decryptError) {
                // إذا فشل فك التشفير، جرب فك الترميز المباشر
                try {
                    const decodedData = this.base64Decode(userCode);
                    userData = JSON.parse(decodedData);
                } catch (parseError) {
                    // إذا فشل كل شيء، أنشئ بيانات افتراضية
                    const deviceId = await this.getAdvancedDeviceFingerprint();
                    userData = {
                        deviceId: deviceId,
                        timestamp: Date.now(),
                        salt: this.generateCryptoRandom(16),
                        securityToken: await this.generateSecurityToken(),
                        version: this.version,
                        securityLevel: this.securityLevel
                    };
                }
            }

            // التحقق من صحة البيانات (مرن أكثر)
            if (!userData.deviceId) {
                userData.deviceId = await this.getAdvancedDeviceFingerprint();
            }
            if (!userData.securityToken) {
                userData.securityToken = await this.generateSecurityToken();
            }
            if (!userData.version) {
                userData.version = this.version;
            }

            // إنشاء بيانات الترخيص المتقدمة
            const licenseData = {
                deviceId: userData.deviceId,
                securityToken: userData.securityToken,
                issuedDate: new Date().toISOString(),
                expiryDate: expiryDays > 0 ? new Date(Date.now() + (expiryDays * 24 * 60 * 60 * 1000)).toISOString() : null,
                notes: notes,
                version: this.version,
                securityLevel: this.securityLevel,
                issuer: 'ADVANCED_LICENSE_SYSTEM',
                signature: await this.generateLicenseSignature(userData.deviceId, expiryDays)
            };

            // تشفير بيانات الترخيص
            const encryptedLicense = await this.encryptAES256(JSON.stringify(licenseData));

            // حفظ في سجل المطور
            this.saveAdvancedDeveloperLicense(licenseData, encryptedLicense);

            return encryptedLicense;
        } catch (error) {
            console.error('خطأ في توليد كود التفعيل المتقدم:', error);
            return null;
        }
    }

    // التحقق من صحة بيانات المستخدم
    validateUserData(userData) {
        const requiredFields = ['deviceId', 'timestamp', 'salt', 'securityToken', 'version'];

        // فحص الحقول المطلوبة
        for (const field of requiredFields) {
            if (!userData[field]) return false;
        }

        // فحص الوقت (لا يزيد عن 24 ساعة)
        const timeDiff = Date.now() - userData.timestamp;
        if (timeDiff > 24 * 60 * 60 * 1000) return false;

        // فحص الإصدار
        if (userData.version !== this.version) return false;

        return true;
    }

    // توليد توقيع الترخيص
    async generateLicenseSignature(deviceId, expiryDays) {
        const signatureData = {
            deviceId: deviceId,
            expiryDays: expiryDays,
            timestamp: Date.now(),
            masterKey: this.encryptionKey,
            challenge: this.generateChallenge()
        };

        return await this.sha256(JSON.stringify(signatureData));
    }

    // حفظ ترخيص المطور المتقدم
    saveAdvancedDeveloperLicense(licenseData, encryptedLicense) {
        const licenses = JSON.parse(localStorage.getItem('advanced_developer_licenses') || '[]');

        const licenseRecord = {
            id: this.generateCryptoRandom(16),
            deviceId: licenseData.deviceId.substring(0, 20) + '...',
            issuedDate: licenseData.issuedDate,
            expiryDate: licenseData.expiryDate,
            notes: licenseData.notes,
            activationCode: encryptedLicense.substring(0, 50) + '...',
            status: 'active',
            securityLevel: licenseData.securityLevel
        };

        licenses.push(licenseRecord);
        localStorage.setItem('advanced_developer_licenses', JSON.stringify(licenses));

        // تحديث الإحصائيات
        this.updateAdvancedStats();
    }

    // تحديث الإحصائيات المتقدمة
    updateAdvancedStats() {
        const licenses = JSON.parse(localStorage.getItem('advanced_developer_licenses') || '[]');
        const now = new Date();

        const stats = {
            total: licenses.length,
            active: licenses.filter(l => l.expiryDate && new Date(l.expiryDate) > now).length,
            expired: licenses.filter(l => l.expiryDate && new Date(l.expiryDate) <= now).length,
            permanent: licenses.filter(l => !l.expiryDate).length,
            today: licenses.filter(l => {
                const issued = new Date(l.issuedDate);
                return issued.toDateString() === now.toDateString();
            }).length,
            thisMonth: licenses.filter(l => {
                const issued = new Date(l.issuedDate);
                return issued.getMonth() === now.getMonth() && issued.getFullYear() === now.getFullYear();
            }).length,
            securityLevels: {
                maximum: licenses.filter(l => l.securityLevel === 'MAXIMUM').length,
                high: licenses.filter(l => l.securityLevel === 'HIGH').length,
                normal: licenses.filter(l => l.securityLevel === 'NORMAL').length
            }
        };

        localStorage.setItem('advanced_license_stats', JSON.stringify(stats));
    }

    // تفعيل الترخيص المتقدم
    async activateAdvancedLicense(activationCode) {
        try {
            // فك تشفير كود التفعيل
            const decryptedLicense = await this.decryptAES256(activationCode);
            const licenseData = JSON.parse(decryptedLicense);

            // التحقق من صحة الترخيص
            if (!await this.validateAdvancedLicense(licenseData)) {
                throw new Error('Invalid license');
            }

            // التحقق من الجهاز
            const currentDeviceId = await this.getAdvancedDeviceFingerprint();
            if (licenseData.deviceId !== currentDeviceId) {
                throw new Error('Device mismatch');
            }

            // التحقق من التوقيع
            const expectedSignature = await this.generateLicenseSignature(
                licenseData.deviceId,
                licenseData.expiryDate ? Math.ceil((new Date(licenseData.expiryDate) - new Date(licenseData.issuedDate)) / (24 * 60 * 60 * 1000)) : 0
            );

            if (licenseData.signature !== expectedSignature) {
                throw new Error('Invalid signature');
            }

            // حفظ الترخيص
            localStorage.setItem('advanced_license', JSON.stringify(licenseData));
            localStorage.setItem('advanced_license_activated', Date.now().toString());

            return true;
        } catch (error) {
            console.error('خطأ في تفعيل الترخيص المتقدم:', error);
            return false;
        }
    }

    // التحقق من صحة الترخيص المتقدم
    async validateAdvancedLicense(licenseData) {
        const requiredFields = ['deviceId', 'issuedDate', 'version', 'securityLevel', 'issuer', 'signature'];

        // فحص الحقول المطلوبة
        for (const field of requiredFields) {
            if (!licenseData[field]) return false;
        }

        // فحص الإصدار
        if (licenseData.version !== this.version) return false;

        // فحص المصدر
        if (licenseData.issuer !== 'ADVANCED_LICENSE_SYSTEM') return false;

        // فحص تاريخ الانتهاء
        if (licenseData.expiryDate && new Date(licenseData.expiryDate) <= new Date()) {
            return false;
        }

        return true;
    }

    // فحص إمكانية الاستخدام المتقدم
    async canUseAdvanced() {
        // فحص الترخيص أولاً
        const license = localStorage.getItem('advanced_license');
        if (license) {
            try {
                const licenseData = JSON.parse(license);
                if (await this.validateAdvancedLicense(licenseData)) {
                    // التحقق من الجهاز مرة أخرى
                    const currentDeviceId = await this.getAdvancedDeviceFingerprint();
                    if (licenseData.deviceId === currentDeviceId) {
                        return {
                            allowed: true,
                            reason: 'licensed',
                            expiryDate: licenseData.expiryDate,
                            daysRemaining: licenseData.expiryDate ?
                                Math.ceil((new Date(licenseData.expiryDate) - new Date()) / (24 * 60 * 60 * 1000)) : null
                        };
                    }
                }
            } catch (error) {
                console.error('خطأ في فحص الترخيص:', error);
            }
        }

        // فحص فترة التجربة
        const usageCount = this.getUsageCount();
        if (usageCount < this.maxUsage) {
            return {
                allowed: true,
                reason: 'trial',
                remaining: this.maxUsage - usageCount
            };
        }

        return {
            allowed: false,
            reason: 'expired',
            message: 'انتهت فترة التجربة. يرجى الحصول على ترخيص للمتابعة.'
        };
    }

    // تتبع الاستخدام مع حماية إضافية
    trackUsage() {
        const usageKey = 'advanced_usage_' + this.sha256(navigator.userAgent + screen.width + screen.height);
        let usage = JSON.parse(localStorage.getItem(usageKey) || '{"count": 0, "dates": []}');

        const today = new Date().toDateString();
        if (!usage.dates.includes(today)) {
            usage.count++;
            usage.dates.push(today);

            // الاحتفاظ بآخر 30 يوم فقط
            if (usage.dates.length > 30) {
                usage.dates = usage.dates.slice(-30);
            }

            localStorage.setItem(usageKey, JSON.stringify(usage));
        }
    }

    // الحصول على عدد مرات الاستخدام
    getUsageCount() {
        const usageKey = 'advanced_usage_' + this.sha256(navigator.userAgent + screen.width + screen.height);
        const usage = JSON.parse(localStorage.getItem(usageKey) || '{"count": 0}');
        return usage.count;
    }

    // إعادة تعيين الاستخدام
    resetUsage() {
        const usageKey = 'advanced_usage_' + this.sha256(navigator.userAgent + screen.width + screen.height);
        localStorage.removeItem(usageKey);
    }

    // إزالة الترخيص
    removeAdvancedLicense() {
        localStorage.removeItem('advanced_license');
        localStorage.removeItem('advanced_license_activated');
    }

    // فحص الترخيص عند بدء التشغيل
    async checkLicense() {
        const canUse = await this.canUseAdvanced();

        if (!canUse.allowed) {
            setTimeout(() => {
                this.showAdvancedLicenseDialog();
            }, 2000);
            return false;
        }

        if (canUse.reason === 'trial') {
            this.showAdvancedTrialNotification(canUse.remaining);
        }

        return true;
    }

    // عرض نافذة الترخيص المتقدمة
    async showAdvancedLicenseDialog() {
        const userCode = await this.generateAdvancedUserCode();

        // إنشاء نافذة الترخيص المتقدمة
        const modal = document.createElement('div');
        modal.className = 'modal active';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(10px);
        `;

        modal.innerHTML = `
            <div style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 20px;
                padding: 40px;
                max-width: 600px;
                width: 90%;
                color: white;
                text-align: center;
                box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
                border: 2px solid rgba(255, 255, 255, 0.1);
            ">
                <div style="margin-bottom: 30px;">
                    <div style="font-size: 4rem; margin-bottom: 20px;">🔐</div>
                    <h2 style="margin: 0; font-size: 2rem; font-weight: 700;">نظام الحماية المتقدم</h2>
                    <p style="margin: 10px 0 0 0; opacity: 0.9;">الإصدار 2.0 - حماية متعددة الطبقات</p>
                </div>

                <div style="background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 25px; margin: 25px 0; backdrop-filter: blur(10px);">
                    <h3 style="margin: 0 0 15px 0; color: #FFD700;">⚠️ انتهت فترة التجربة المجانية</h3>
                    <p style="margin: 0; line-height: 1.6;">
                        لقد استخدمت النظام ${this.maxUsage} مرات. للمتابعة، يرجى الحصول على ترخيص من المطور.
                    </p>
                </div>

                <div style="background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 25px; margin: 25px 0;">
                    <h4 style="margin: 0 0 15px 0;">🔑 الخطوة 1: كود الجهاز المتقدم</h4>
                    <div style="background: rgba(0, 0, 0, 0.3); border-radius: 10px; padding: 15px; margin: 15px 0;">
                        <textarea readonly style="
                            width: 100%;
                            height: 80px;
                            background: transparent;
                            border: none;
                            color: #FFD700;
                            font-family: 'Courier New', monospace;
                            font-size: 0.9rem;
                            resize: none;
                            outline: none;
                        ">${userCode}</textarea>
                    </div>
                    <button onclick="navigator.clipboard.writeText('${userCode}').then(() => alert('تم نسخ الكود!'))" style="
                        background: #4CAF50;
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 8px;
                        cursor: pointer;
                        font-weight: 600;
                        margin: 10px;
                    ">📋 نسخ الكود</button>
                </div>

                <div style="background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 25px; margin: 25px 0;">
                    <h4 style="margin: 0 0 15px 0;">📞 الخطوة 2: التواصل مع المطور</h4>
                    <div style="text-align: right; line-height: 2;">
                        <p>📧 البريد الإلكتروني: <strong><EMAIL></strong></p>
                        <p>📱 الهاتف: <strong>+966 50 123 4567</strong></p>
                        <p>💬 واتساب: <strong>+966 50 123 4567</strong></p>
                    </div>
                </div>

                <div style="background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 25px; margin: 25px 0;">
                    <h4 style="margin: 0 0 15px 0;">🔓 الخطوة 3: إدخال كود التفعيل</h4>
                    <input type="text" id="advanced-activation-code" placeholder="الصق كود التفعيل هنا..." style="
                        width: 100%;
                        padding: 15px;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        border-radius: 10px;
                        background: rgba(255, 255, 255, 0.1);
                        color: white;
                        font-size: 1rem;
                        margin: 10px 0;
                        outline: none;
                    ">
                    <button onclick="activateAdvancedLicense()" style="
                        background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
                        color: white;
                        border: none;
                        padding: 15px 30px;
                        border-radius: 10px;
                        cursor: pointer;
                        font-weight: 700;
                        font-size: 1.1rem;
                        margin: 15px 10px;
                        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
                    ">🚀 تفعيل الترخيص</button>
                </div>

                <div style="background: rgba(255, 255, 255, 0.05); border-radius: 15px; padding: 20px; margin: 25px 0; border: 1px solid rgba(255, 255, 255, 0.1);">
                    <h4 style="margin: 0 0 15px 0; color: #FFD700;">🛡️ مميزات النظام المتقدم:</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; text-align: right; font-size: 0.9rem;">
                        <div>✅ تشفير AES-256</div>
                        <div>✅ حماية من الآلات الافتراضية</div>
                        <div>✅ كشف المحاكيات</div>
                        <div>✅ تتبع السلوك</div>
                        <div>✅ نبضة قلب للخادم</div>
                        <div>✅ حماية من التلاعب</div>
                    </div>
                </div>

                <p style="font-size: 0.8rem; opacity: 0.7; margin: 20px 0 0 0;">
                    نظام الحماية المتقدم v2.0 - جميع الحقوق محفوظة
                </p>
            </div>
        `;

        document.body.appendChild(modal);

        // منع إغلاق النافذة
        modal.addEventListener('click', (e) => {
            e.stopPropagation();
        });
    }

    // عرض إشعار فترة التجربة المتقدم
    showAdvancedTrialNotification(remaining) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            z-index: 9999;
            max-width: 350px;
            border: 2px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        `;

        notification.innerHTML = `
            <div style="display: flex; align-items: center; margin-bottom: 10px;">
                <span style="font-size: 1.5rem; margin-left: 10px;">⚠️</span>
                <strong>تحذير - النظام المتقدم</strong>
            </div>
            <p style="margin: 0; line-height: 1.5;">
                متبقي <strong>${remaining}</strong> استخدام${remaining > 1 ? 'ات' : ''} من فترة التجربة المجانية.
            </p>
            <button onclick="this.parentElement.remove()" style="
                background: rgba(255, 255, 255, 0.2);
                border: none;
                color: white;
                padding: 8px 16px;
                border-radius: 8px;
                cursor: pointer;
                margin-top: 10px;
                float: left;
            ">حسناً</button>
        `;

        document.body.appendChild(notification);

        // إزالة الإشعار تلقائياً بعد 10 ثوان
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 10000);
    }
}

// تفعيل الترخيص المتقدم (دالة عامة)
async function activateAdvancedLicense() {
    const code = document.getElementById('advanced-activation-code').value.trim();

    if (!code) {
        alert('يرجى إدخال كود التفعيل');
        return;
    }

    const success = await advancedLicenseManager.activateAdvancedLicense(code);

    if (success) {
        alert('تم تفعيل الترخيص بنجاح! سيتم إعادة تشغيل النظام...');
        location.reload();
    } else {
        alert('كود التفعيل غير صحيح أو منتهي الصلاحية');
    }
}

// إنشاء مثيل من النظام المتقدم
const advancedLicenseManager = new AdvancedLicenseManager();
