// معالج الملفات - نظام المراسلات
class FileHandler {
    constructor() {
        this.maxFileSize = 10 * 1024 * 1024; // 10 ميجابايت
        this.allowedTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'text/plain',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
        this.fileExtensions = {
            'application/pdf': 'pdf',
            'application/msword': 'doc',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
            'image/jpeg': 'jpg',
            'image/jpg': 'jpg',
            'image/png': 'png',
            'image/gif': 'gif',
            'text/plain': 'txt',
            'application/vnd.ms-excel': 'xls',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx'
        };
    }

    // التحقق من صحة الملف
    validateFile(file) {
        const errors = [];

        // التحقق من حجم الملف
        if (file.size > this.maxFileSize) {
            errors.push(`حجم الملف كبير جداً. الحد الأقصى ${this.formatFileSize(this.maxFileSize)}`);
        }

        // التحقق من نوع الملف
        if (!this.allowedTypes.includes(file.type)) {
            errors.push('نوع الملف غير مدعوم');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    // تحويل حجم الملف إلى تنسيق قابل للقراءة
    formatFileSize(bytes) {
        if (bytes === 0) return '0 بايت';
        
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // الحصول على أيقونة الملف حسب النوع
    getFileIcon(fileType) {
        const iconMap = {
            'application/pdf': 'fas fa-file-pdf',
            'application/msword': 'fas fa-file-word',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'fas fa-file-word',
            'image/jpeg': 'fas fa-file-image',
            'image/jpg': 'fas fa-file-image',
            'image/png': 'fas fa-file-image',
            'image/gif': 'fas fa-file-image',
            'text/plain': 'fas fa-file-alt',
            'application/vnd.ms-excel': 'fas fa-file-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'fas fa-file-excel'
        };

        return iconMap[fileType] || 'fas fa-file';
    }

    // قراءة الملف كـ Base64
    readFileAsBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                resolve(e.target.result);
            };
            
            reader.onerror = function(e) {
                reject(new Error('خطأ في قراءة الملف'));
            };
            
            reader.readAsDataURL(file);
        });
    }

    // معالجة رفع الملفات
    async handleFileUpload(files) {
        const processedFiles = [];
        const errors = [];

        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const validation = this.validateFile(file);

            if (!validation.isValid) {
                errors.push({
                    fileName: file.name,
                    errors: validation.errors
                });
                continue;
            }

            try {
                const base64Data = await this.readFileAsBase64(file);
                
                const fileData = {
                    id: this.generateFileId(),
                    name: file.name,
                    type: file.type,
                    size: file.size,
                    data: base64Data,
                    uploadDate: new Date().toISOString(),
                    extension: this.fileExtensions[file.type] || 'unknown'
                };

                processedFiles.push(fileData);
            } catch (error) {
                errors.push({
                    fileName: file.name,
                    errors: ['خطأ في معالجة الملف']
                });
            }
        }

        return {
            files: processedFiles,
            errors
        };
    }

    // إنشاء معرف فريد للملف
    generateFileId() {
        return 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // إنشاء عنصر HTML لعرض الملف المرفوع
    createFileElement(fileData, showActions = true) {
        const fileElement = document.createElement('div');
        fileElement.className = 'uploaded-file';
        fileElement.setAttribute('data-file-id', fileData.id);

        const fileIcon = this.getFileIcon(fileData.type);
        const fileSize = this.formatFileSize(fileData.size);

        fileElement.innerHTML = `
            <div class="file-info">
                <i class="${fileIcon} file-icon"></i>
                <div class="file-details">
                    <div class="file-name">${fileData.name}</div>
                    <div class="file-size">${fileSize}</div>
                </div>
            </div>
            ${showActions ? `
                <div class="file-actions">
                    <button type="button" class="file-action" onclick="previewFile('${fileData.id}')" title="معاينة">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button type="button" class="file-action" onclick="downloadFile('${fileData.id}')" title="تحميل">
                        <i class="fas fa-download"></i>
                    </button>
                    <button type="button" class="file-action danger" onclick="removeFile('${fileData.id}')" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            ` : ''}
        `;

        return fileElement;
    }

    // معاينة الملف
    previewFile(fileId, files) {
        const file = files.find(f => f.id === fileId);
        if (!file) {
            showNotification('الملف غير موجود', 'error');
            return;
        }

        const modal = document.getElementById('file-preview-modal');
        const title = document.getElementById('file-preview-title');
        const content = document.getElementById('file-preview-content');
        const downloadBtn = document.getElementById('download-file-btn');

        title.textContent = file.name;
        
        // تحديد نوع المعاينة حسب نوع الملف
        if (file.type.startsWith('image/')) {
            content.innerHTML = `
                <div class="image-preview">
                    <img src="${file.data}" alt="${file.name}" style="max-width: 100%; height: auto; border-radius: 8px;">
                </div>
            `;
        } else if (file.type === 'application/pdf') {
            content.innerHTML = `
                <div class="pdf-preview">
                    <embed src="${file.data}" type="application/pdf" width="100%" height="600px" />
                    <p class="text-center mt-3">
                        <small>إذا لم يظهر الملف، يمكنك <a href="${file.data}" download="${file.name}">تحميله من هنا</a></small>
                    </p>
                </div>
            `;
        } else if (file.type === 'text/plain') {
            // قراءة محتوى الملف النصي
            fetch(file.data)
                .then(response => response.text())
                .then(text => {
                    content.innerHTML = `
                        <div class="text-preview">
                            <pre style="white-space: pre-wrap; background: #f8f9fa; padding: 20px; border-radius: 8px; max-height: 500px; overflow-y: auto;">${text}</pre>
                        </div>
                    `;
                })
                .catch(() => {
                    content.innerHTML = `
                        <div class="preview-error">
                            <i class="fas fa-exclamation-triangle"></i>
                            <p>لا يمكن معاينة هذا الملف</p>
                            <button class="btn btn-primary" onclick="downloadFile('${fileId}')">تحميل الملف</button>
                        </div>
                    `;
                });
        } else {
            content.innerHTML = `
                <div class="preview-not-supported">
                    <i class="fas fa-file-alt" style="font-size: 4rem; color: #6b7280; margin-bottom: 20px;"></i>
                    <h3>معاينة غير متاحة</h3>
                    <p>لا يمكن معاينة هذا النوع من الملفات في المتصفح</p>
                    <div class="file-info-preview">
                        <p><strong>اسم الملف:</strong> ${file.name}</p>
                        <p><strong>نوع الملف:</strong> ${file.type}</p>
                        <p><strong>حجم الملف:</strong> ${this.formatFileSize(file.size)}</p>
                    </div>
                    <button class="btn btn-primary mt-3" onclick="downloadFile('${fileId}')">
                        <i class="fas fa-download"></i>
                        تحميل الملف
                    </button>
                </div>
            `;
        }

        // تحديث زر التحميل
        downloadBtn.onclick = () => this.downloadFile(fileId, files);

        // عرض النافذة المنبثقة
        modal.classList.add('active');
        modal.style.display = 'flex';
    }

    // تحميل الملف
    downloadFile(fileId, files) {
        const file = files.find(f => f.id === fileId);
        if (!file) {
            showNotification('الملف غير موجود', 'error');
            return;
        }

        try {
            // إنشاء رابط تحميل
            const link = document.createElement('a');
            link.href = file.data;
            link.download = file.name;
            link.style.display = 'none';
            
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showNotification('تم بدء التحميل', 'success');
        } catch (error) {
            console.error('خطأ في تحميل الملف:', error);
            showNotification('خطأ في تحميل الملف', 'error');
        }
    }

    // ضغط الصورة (اختياري)
    compressImage(file, maxWidth = 1920, quality = 0.8) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = function() {
                // حساب الأبعاد الجديدة
                let { width, height } = img;
                
                if (width > maxWidth) {
                    height = (height * maxWidth) / width;
                    width = maxWidth;
                }

                canvas.width = width;
                canvas.height = height;

                // رسم الصورة المضغوطة
                ctx.drawImage(img, 0, 0, width, height);

                // تحويل إلى Base64
                canvas.toBlob((blob) => {
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result);
                    reader.readAsDataURL(blob);
                }, file.type, quality);
            };

            img.src = URL.createObjectURL(file);
        });
    }

    // إنشاء صورة مصغرة
    createThumbnail(file, size = 150) {
        return new Promise((resolve) => {
            if (!file.type.startsWith('image/')) {
                resolve(null);
                return;
            }

            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = function() {
                canvas.width = size;
                canvas.height = size;

                // حساب موضع الصورة للحصول على مربع مثالي
                const scale = Math.max(size / img.width, size / img.height);
                const x = (size - img.width * scale) / 2;
                const y = (size - img.height * scale) / 2;

                ctx.drawImage(img, x, y, img.width * scale, img.height * scale);

                canvas.toBlob((blob) => {
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result);
                    reader.readAsDataURL(blob);
                }, 'image/jpeg', 0.7);
            };

            img.src = URL.createObjectURL(file);
        });
    }

    // تصدير الملفات كـ ZIP (محاكاة)
    exportFilesAsZip(files, correspondenceTitle) {
        // في التطبيق الحقيقي، يمكن استخدام مكتبة JSZip
        // هنا سنقوم بتحميل الملفات واحداً تلو الآخر
        
        if (files.length === 0) {
            showNotification('لا توجد ملفات للتصدير', 'warning');
            return;
        }

        showNotification(`جاري تحميل ${files.length} ملف...`, 'info');

        files.forEach((file, index) => {
            setTimeout(() => {
                this.downloadFile(file.id, files);
            }, index * 500); // تأخير بسيط بين التحميلات
        });
    }

    // تنظيف البيانات المؤقتة
    cleanup() {
        // إزالة URLs المؤقتة إذا كانت موجودة
        const tempUrls = document.querySelectorAll('[data-temp-url]');
        tempUrls.forEach(element => {
            const url = element.getAttribute('data-temp-url');
            if (url) {
                URL.revokeObjectURL(url);
            }
        });
    }

    // إحصائيات الملفات
    getFileStatistics(allCorrespondences) {
        let totalFiles = 0;
        let totalSize = 0;
        const fileTypes = {};

        allCorrespondences.forEach(correspondence => {
            if (correspondence.files && correspondence.files.length > 0) {
                correspondence.files.forEach(file => {
                    totalFiles++;
                    totalSize += file.size;
                    
                    const extension = file.extension || 'unknown';
                    fileTypes[extension] = (fileTypes[extension] || 0) + 1;
                });
            }
        });

        return {
            totalFiles,
            totalSize: this.formatFileSize(totalSize),
            fileTypes,
            averageFileSize: totalFiles > 0 ? this.formatFileSize(totalSize / totalFiles) : '0 بايت'
        };
    }
}

// إنشاء مثيل واحد من معالج الملفات
const fileHandler = new FileHandler();
